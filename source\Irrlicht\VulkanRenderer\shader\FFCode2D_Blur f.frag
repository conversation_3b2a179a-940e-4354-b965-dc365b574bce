#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;
#define PI 3.1415926
layout (binding = 0) uniform UBO 
{


				float blurStep;
				float blurWeightMul;
				float sigma, sigma2;
				int blurSize, addPattern, i02, i03;
				float apLenMul, apHeightMul, apHeightAdd, f13;
				float apf1, apf2, Directions, Quality;
				vec4 matRtt;
				vec4 pad[15 - 5];

	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w
} ubo;

layout (binding = 2)  uniform sampler2D g_tex0_sampler;




	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;




//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;


float blurknl(in float x) {
	return 0.39894*exp(-0.5*x*x/(ubo.sigma2))/ubo.sigma;
}

void main( )
{
    float Pi = 6.28318530718; // Pi*2
    
    // GAUSSIAN BLUR SETTINGS {{{
    float Directions = ubo.Directions; // BLUR DIRECTIONS (Default 16.0 - More is better but slower)
    float Quality = ubo.Quality; // BLUR QUALITY (Default 4.0 - More is better but slower)
    float Size = ubo.blurSize; // BLUR SIZE (Radius)
    // GAUSSIAN BLUR SETTINGS }}}
   
    vec2 Radius = Size/ubo.res;
    
    // Normalized pixel coordinates (from 0 to 1)
    vec2 uv = i_tex0;
    // Pixel colour
    vec4 Color = texture(g_tex0_sampler, uv);
    
    // Blur calculations
    for( float d=0.0; d<Pi; d+=Pi/Directions)
    {
		for(float i=1.0/Quality; i<=1.0; i+=1.0/Quality)
        {
			Color += texture( g_tex0_sampler, uv+vec2(cos(d),sin(d))*Radius*i);		
        }
    }
    
    // Output to screen
    Color /= Quality * Directions - 15.0;
    Color*= ubo.blurWeightMul;
    outFragColor =  Color;
}