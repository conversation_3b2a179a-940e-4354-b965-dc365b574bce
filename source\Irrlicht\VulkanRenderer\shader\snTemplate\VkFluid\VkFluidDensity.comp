#version 460
#extension GL_GOOGLE_include_directive : enable
#define COMPUTE_SHADER
#include "VkFluidCommon.glsl"
/**
 * =================================================================
 * VkFluidDensity.comp - Grid-based SPH Density Computation (BufferB Pattern)
 * =================================================================
 * 
 * Follows ShaderToy wt/BufferB.glsl SPH density computation pattern
 * Uses grid-based particle storage for efficient neighborhood iteration
 * 
 * Algorithm (true BufferB pattern):
 * 1. For each voxel in 3D volume texture, treat it as a virtual particle
 * 2. Iterate through 5x5x5 neighborhood grid cells only (125 cells max)
 * 3. Load max 2 particles per grid cell using LOAD3D equivalent
 * 4. Accumulate density contributions using SPH kernels
 * 5. Store voxel density result in 3D texture
 * =================================================================
 */

layout(local_size_x = 8, local_size_y = 8, local_size_z = 8) in;

// Grid-based particle storage (simplified - single float per cell)
layout(set = 0, binding = 1, rgba32f) uniform image3D particleGridTexture;  // Single float: accumulated mass

// Secondary grid-based particle storage
layout(set = 0, binding = 6, rgba32f) uniform image3D particleGridTextureSecondary;  // Secondary particles
 
// Output 3D density texture - now RGBA16F format for color(RGB) + density/alpha(A)
layout(set = 0, binding = 3, rgba16f) uniform image3D densityTexture;

// SPH Constants (matching BufferB.glsl parameters)
#define PI 3.1415926535
#define SCENE_SCALE 1.0               // Scene scale factor to match original coordinates
#define KERNEL_RADIUS (uniforms.kernelRadius * SCENE_SCALE)  // SPH kernel radius (scaled for scene coordinates)
#define NEIGHBORHOOD_SIZE 2             // Search radius in grid cells (5x5x5 = ±2)

// Enhanced particle structure to track color and density
struct VirtualParticle {
    vec3 pos;
    vec3 color;             // Accumulated color (RGB)
    float totalDensity;     // Total density (Alpha)
    float regularDensity;   // Density from regular particles
    float diffuseDensity;   // Density from diffuse particles
};

#define PI 3.1415926535
#define TWO_PI 6.28318530718

// 光照方向 (Light direction)
#define light_dir normalize(vec3(0.741,1.000,0.580))

// =================================================================
// Material Index Packing/Unpacking Functions
// =================================================================

// Pack material index into mass field using sign encoding (compatible with float16)
// Encoding: +materialIndex+0.5 for normal particles, -(materialIndex+0.5) for diffuse particles
// Examples: +0.5 = material 0 normal, **** = material 1 normal, -0.5 = material 0 diffuse, -1.5 = material 1 diffuse
float packMaterialMass(uint materialIndex, bool isDiffuse) {
    float encoded = float(materialIndex) + 0.5;
    return isDiffuse ? -encoded : encoded;
}

// Unpack material index and particle type from mass field
void unpackMaterialMass(float packedMass, out uint materialIndex, out bool isDiffuse) {
    if (packedMass >= 0.0) {
        // Positive = normal particle
        isDiffuse = false;
        materialIndex = uint(packedMass);  // floor(packedMass)
    } else {
        // Negative = diffuse particle
        isDiffuse = true;
        materialIndex = uint(-packedMass);  // floor(-packedMass)
    }
}

// Get actual particle mass based on type (normal vs diffuse)
float getParticleMass(bool isDiffuse) {
    return isDiffuse ? 0.5 : 1.0;  // Diffuse particles have half mass
}

// =================================================================
// Material Color Functions
// =================================================================

// Get material color from uniform buffer using material index
vec4 getMaterialColor(uint materialIndex) {
    // Clamp material index to valid range (0-31 based on mtrColor array size)
    uint clampedIndex = min(materialIndex, 31u);
    return uniforms.mtrColor[clampedIndex];
}

// Blend two colors based on density weights
vec3 blendColors(vec3 existingColor, float existingDensity, vec3 newColor, float newDensity) {
    if (existingDensity + newDensity == 0.0) {
        return vec3(0.0);
    }
    
    // Weighted average based on density contributions
    float totalDensity = existingDensity + newDensity;
    float existingWeight = existingDensity / totalDensity;
    float newWeight = newDensity / totalDensity;
    
    return existingColor * existingWeight + newColor * newWeight;
}

// =================================================================
// SPH核函数 (SPH Kernel Functions)
// =================================================================
// 平方函数 (Square function)
float sqr(float x)
{
    return x * x;
}

// 立方函数 (Cube function)
float cub(float x)
{
    return x*x*x;
}

// 高斯核函数 (Gaussian kernel function)
// 用于密度和压力计算 (Used for density and pressure calculations)
float Gaussian(float r, float d)
{
    float norm = 1.0/(cub(d)*sqrt(cub(TWO_PI)));
    return norm * exp(-0.5*sqr(r/d));
}

// 高斯核函数梯度 (Gaussian kernel gradient)
// 用于压力力计算 (Used for pressure force calculations)
vec3 GaussianGrad(vec3 dx, float d)
{
    float norm = 1.0/(cub(d)*sqrt(cub(TWO_PI)));
    float r = length(dx);
    return - (norm/sqr(d)) * exp(-0.5*sqr(r/d)) * dx;
}

// 高斯核函数梯度扩展版本 (Extended Gaussian kernel gradient)
vec4 GaussianGrad2(vec3 dx, float d)
{
    float norm = 1.0/(cub(d)*sqrt(cub(TWO_PI)));
    float r = length(dx);
    return norm * exp(-0.5*sqr(r/d)) * vec4(-dx /sqr(d), 1.0);
}
// SPH Kernel Functions (from ShaderToy wt_common.glsl)
float Gaussian2(float r, float h) {
    if (r > h) return 0.0;
    
    // Simplified kernel that works better with scaled coordinates
    // Using cubic spline kernel instead of Gaussian with problematic normalization
    float q = r / h;
    if (q >= 1.0) return 0.0;
    
    // Cubic spline kernel (more stable for large scale factors)
    float factor = 1.0 - q;
    return factor  ;
}

// Gaussian density kernel (GD macro from BufferB) - now using cubic spline
float GD(float r, float h) {
    return Gaussian2(r, h);
}


// Load particle data from primary grid (regular particles)
// Returns: xyz = position, w = packed mass (with material index)
vec4 LoadPosMassFromGrid(ivec3 gridPos) {
    // Check grid bounds using uniform instead of expensive imageSize() call
    ivec3 gridRes = ivec3(uniforms.volumeResX, uniforms.volumeResY, uniforms.volumeResZ);
    if (any(lessThan(gridPos, ivec3(0))) || any(greaterThanEqual(gridPos, gridRes))) {
        return vec4(0.0);
    }
    return imageLoad(particleGridTexture, gridPos);
}

// Load particle data from secondary grid (diffuse particles)  
// Returns: xyz = position, w = packed mass (with material index)
vec4 LoadPosMassFromGridSecondary(ivec3 gridPos) {
    // Check grid bounds using uniform instead of expensive imageSize() call
    ivec3 gridRes = ivec3(uniforms.volumeResX, uniforms.volumeResY, uniforms.volumeResZ);
    if (any(lessThan(gridPos, ivec3(0))) || any(greaterThanEqual(gridPos, gridRes))) {
        return vec4(0.0);
    }
    return imageLoad(particleGridTextureSecondary, gridPos);
}

/**
 * Optimized density contribution - inlined and reduced function calls
 * Now processes both grids in single function to reduce call overhead
 */
void AddDensityFromGridCellOptimized(inout VirtualParticle pV, ivec3 gridPos, float kernelRadius, ivec3 gridRes) {
    // Early bounds check to avoid imageLoad calls
    if (any(lessThan(gridPos, ivec3(0))) || any(greaterThanEqual(gridPos, gridRes))) {
        return;
    }
    
    // Process primary grid texture particle (regular particles)
    vec4 p = imageLoad(particleGridTexture, gridPos);
    float packedMass = p.w;
    if (packedMass > 0.0) {
        // Fast distance calculation - avoid length() when possible
        vec3 delta = p.xyz - pV.pos;
        float distanceSq = dot(delta, delta);

        
        // Early rejection based on kernel radius
        if (distanceSq > uniforms.kernelRadiusSq) return;

        float distance = sqrt(distanceSq);
        // Inline unpacking for better performance
        uint materialIndex = uint(packedMass);  // floor(packedMass)
        float actualMass = 1.0;  // Normal particles have mass 1.0
        
        // Inline kernel calculation
        float q = distance / kernelRadius;
        if (q < 1.0) {
            float kernelValue = 1.0 - q;  // Simplified cubic spline
            
            // Get material color - clamp index inline
            uint clampedIndex = min(materialIndex, 31u);
            vec4 materialColor = uniforms.mtrColor[clampedIndex];
            
#if MTR_A2D
            float contribution = actualMass * kernelValue * materialColor.a;
#else
            float contribution = actualMass * kernelValue;
#endif
            
            // Fast color blending - avoid function call
            if (pV.totalDensity > 0.0) {
                float totalDensity = pV.totalDensity + contribution;
                float existingWeight = pV.totalDensity / totalDensity;
                float newWeight = contribution / totalDensity;
                pV.color = pV.color * existingWeight + materialColor.rgb * newWeight;
            } else {
                pV.color = materialColor.rgb;
            }
            
            pV.regularDensity += contribution;
            pV.totalDensity += contribution;
        }
    }
    
 #if RENDER_DIFFUSE
    // Process secondary grid if enabled
    if (uniforms.renderDiffuse != 0) {
        vec4 pSecondary = imageLoad(particleGridTextureSecondary, gridPos);
        float packedMassSecondary = pSecondary.w;
        if (packedMassSecondary < 0.0) {
            vec3 delta = pSecondary.xyz - pV.pos;
            float distance = sqrt(dot(delta, delta));
            
            if (distance <= kernelRadius) {
                float q = distance / kernelRadius;
                if (q < 1.0) {
                    float kernelValue = 1.0 - q;
                    float contribution = 0.5 * kernelValue * (MTR_A2D==0?10.0:1.0);
                    
                    // Simple white color for diffuse particles
                    if (pV.totalDensity > 0.0) {
                        float totalDensity = pV.totalDensity + contribution;
                        float existingWeight = pV.totalDensity / totalDensity;
                        float newWeight = contribution / totalDensity;
                        pV.color = pV.color * existingWeight + vec3(1.0) * newWeight;
                    } else {
                        pV.color = vec3(1.0);
                    }
                    
                    pV.diffuseDensity += contribution;
                    pV.totalDensity += contribution;
                }
            }
        }
    }
#endif
}

/**
 * Convert world position to grid coordinates
 */
ivec3 worldToGrid(vec3 worldPos) {
    vec3 relativePos = (worldPos - uniforms.volumeMin) / uniforms.gridCellSize;
    return ivec3(floor(relativePos));
}

/**
 * Convert grid coordinates to world position (cell center)
 */
vec3 gridToWorld(ivec3 gridPos) {
    return uniforms.volumeMin + (vec3(gridPos) + 0.5) * uniforms.gridCellSize; 
}

/**
 * Main density computation following BufferB.glsl pattern
 * Optimized to avoid expensive imageSize() calls per thread
 */
void main() {
    ivec3 voxelCoord = ivec3(gl_GlobalInvocationID.xyz);   // (0.5,0,0) to (1,0.5,1) ?
    
    // Use uniform instead of expensive imageSize() call
    ivec3 volumeResolution = ivec3(uniforms.volumeResX, uniforms.volumeResY, uniforms.volumeResZ);
 
    // Check bounds
    if (any(greaterThanEqual(voxelCoord, volumeResolution))) {
        return;
    }
    
    // Convert voxel coordinate to world position (voxel center)
    vec3 voxelWorldPos = uniforms.volumeMin + 
        (vec3(voxelCoord) + 0.5) / vec3(volumeResolution) * uniforms.volumeSize;
    
    // Convert voxel world position to grid coordinate
    ivec3 voxelGridCoord = worldToGrid(voxelWorldPos);
    
    // Create virtual voxel particle for density and color accumulation
    VirtualParticle pV;
    pV.pos = voxelWorldPos;
    pV.color = vec3(0.0);           // Initialize color to black
    pV.totalDensity = 0.0;          // Initialize total density
    pV.regularDensity = 0.0;        // Initialize regular density
    pV.diffuseDensity = 0.0;        // Initialize diffuse density
    
    // Cache values for performance
    float cachedKernelRadius = KERNEL_RADIUS;
    ivec3 gridRes = ivec3(uniforms.volumeResX, uniforms.volumeResY, uniforms.volumeResZ);
    
    // =================================================================
    // Neighborhood iteration (BufferB pattern - 5x5x5 = 125 cells max)
    // Optimized with early termination and reduced function calls
    // =================================================================
    
    // Optional: Dynamic neighborhood size based on kernel radius and grid cell size
    // This reduces iterations when kernel is small relative to grid
    int dynamicNeighborSize = max(1, int(ceil(cachedKernelRadius / uniforms.gridCellSize)));
    int actualNeighborSize = min(NEIGHBORHOOD_SIZE, dynamicNeighborSize);
    
    for (int i = -actualNeighborSize; i <= actualNeighborSize; i++) {
        for (int j = -actualNeighborSize; j <= actualNeighborSize; j++) {
            for (int k = -actualNeighborSize; k <= actualNeighborSize; k++) {
                
                // Calculate neighbor grid cell coordinates
                ivec3 neighborGridCoord = voxelGridCoord + ivec3(i, j, k);
                
                // Add density contribution from neighbor grid cell
                AddDensityFromGridCellOptimized(pV, neighborGridCoord, cachedKernelRadius, gridRes);
            }
        }
    }
    
#if 0 // blend with existing
    // Read existing color and density from texture for blending
    vec4 existingData = imageLoad(densityTexture, voxelCoord);
    vec3 existingColor = existingData.rgb;
    float existingDensity = existingData.a;
    
    // Blend new color with existing color based on density weights
    vec3 finalColor = blendColors(existingColor, existingDensity, pV.color, pV.totalDensity);
    float finalDensity = existingDensity + pV.totalDensity;
#else // replace
    vec3 finalColor = pV.color;
    float finalDensity = pV.totalDensity;
#endif
    // Clamp density to reasonable range for alpha blending
   // finalDensity = clamp(finalDensity, 0.0, 1.0);
    
    // Store final voxel data - RGBA16F format: RGB = color, A = density/alpha
    imageStore(densityTexture, voxelCoord, vec4(finalColor, finalDensity));
    
    // Debug alternatives - uncomment one to test different aspects:
    
    // 1. Show only regular particles in red
    //imageStore(densityTexture, voxelCoord, vec4(pV.regularDensity, 0.0, 0.0, pV.regularDensity));
    
    // 2. Show only diffuse particles in blue  
    //imageStore(densityTexture, voxelCoord, vec4(0.0, 0.0, pV.diffuseDensity, pV.diffuseDensity));
    
    // 3. Show material colors without density weighting
    //imageStore(densityTexture, voxelCoord, vec4(pV.color, pV.totalDensity > 0.0 ? 1.0 : 0.0));
    
    // 4. Test pattern - should show red gradient
    //imageStore(densityTexture, voxelCoord, vec4(float(voxelCoord.x) / float(volumeResolution.x), 0.0, 0.0, 1.0));
} 