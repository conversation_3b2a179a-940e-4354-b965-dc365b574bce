#version 460
#extension GL_GOOGLE_include_directive : enable
#define USE_SPECULAR 0


#include "FFCodeHeader.glsl"

	layout(location = 0) in vec3 i_pos;
	layout(location = 1) in vec3 i_norm;
	layout(location = 2) in vec4 i_color;
	layout(location = 3) in vec2 i_tex0;





	layout(location = 0) out vec2 o_tex0;
	layout(location = 1) out vec2 o_ot;       //world space normal
	layout(location = 2) out vec4 o_colorD;
	layout (location = 3) out flat int o_type;

void main( )   
{
	
	vec4 worldPos = vec4(i_pos,1) * g_mWorld ;
	vec4 cameraPos = worldPos * g_mView ; //Save cameraPos for fog calculations
	gl_Position =  cameraPos * g_mProj;
	o_colorD = i_color.bgra *g_material.Diffuse;
 
	//o_tex0 = i_tex0 ;

	vec2 vtxpos = vec2((gl_VertexIndex & 2) >> 1,((gl_VertexIndex + 3) & 2) >> 1);//0,1  0,0  1,0  1,1
 	o_type=int(i_norm.z);
	o_tex0 = vtxpos;
	o_ot=i_tex0;
	gl_Position.xy += ( vec2(2.f,-2.f)*vtxpos.xy+vec2(-1.f,1.f))*i_norm.xy/vec2(fv,fv1) *gl_Position.w;
	
}


