#version 460
#extension GL_GOOGLE_include_directive : enable

precision highp float;
// References HLSL
// Flow control: http://msdn.microsoft.com/en-us/library/bb509600(VS.85).aspx
// Semantics: http://msdn.microsoft.com/en-us/library/bb509647(VS.85).aspx
#define MAX_LIGHTS 									2
#define EMT_LIGHTMAP								2
#define EMT_LIGHTMAP_ADD							3
#define PI  3.1415927
#define PI2 6.2831853
#define USE_SPECULAR 0


#include "FFCodeHeader.glsl"
#include "FwPtcDistanceFunctions.glsl"
// Constants




layout (binding = 2)  uniform sampler2D g_tex0_sampler;





layout(location = 0) in vec2 i_tex0;
layout(location = 1) in vec2 i_ot;       //world space normal
layout(location = 2) in vec4 i_colorD;
layout(location = 3) in flat int i_type;
//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;

float sdCircle( in vec2 p, in float r ) 
{
    return length(p)-r;
}

float sdBox( in vec2 p, in vec2 b )
{
    vec2 d = abs(p)-b;
    return length(max(d,0.0)) + min(max(d.x,d.y),0.0);
}
float sdRoundBox( in vec2 p, in vec2 b, in float r ) 
{
 
    vec2 q = abs(p)-b+r;
    return min(max(q.x,q.y),0.0) + length(max(q,0.0)) - r;
}
// adding pixel shader
void main()
{
	vec2 pos=i_tex0.xy*2.0-vec2(1.0);
	float c,ca=0.0;
	switch (i_type) {
		case 1:{
			float r= .5+ i_ot.x*0.2*sin(gTime*PI2);
			float d=abs(sdCircle(pos,r));
			c= d<0.2?cos(d*2.5*PI)  :0;
		} break;
		default: {
			float r= .7+ i_ot.x*0.1*sin(gTime*PI2);
			float d=abs(sdRoundBox(pos,vec2(r,r),0.2));
			c= d<0.2?cos(d*5*PI)  :0;
		} break;
	}
	outFragColor = i_colorD*c;
}