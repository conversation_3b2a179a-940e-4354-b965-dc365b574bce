// Constants
#define MAX_LIGHTS 									2
#define EMT_LIGHTMAP								2
#define EMT_LIGHTMAP_ADD							3



// adding needed structs 
struct ColorsOutput
{
    vec4 Diffuse;
    vec4 Specular;
};

struct Light
{ 
    vec4 Position;
    vec4 Diffuse;
    vec4 Specular;
    vec4 Ambient;
    vec4 Atten;
};

struct Material
{ 
	vec4	Ambient;
	vec4	Diffuse;
	vec4	Specular;
	vec4	Emissive;
	float	Shininess;
	int		Type;
	int		id,shadowType;
};



layout(binding = 0) readonly uniform  cbPerFrame
{
    mat4 g_mView;
    mat4 g_mProj;
	mat4 g_mLightVP;
	vec3  g_eyePos;
	float  pad_000000000000000;
};


// adding constant buffer for transform matrices
layout( binding = 1) readonly uniform  cbPerDraw 
{
    mat4 g_mWorld;
	mat4 g_mInvWV;
	Material	g_material;	
	Light		g_lights[MAX_LIGHTS];
	int     g_bEnableLighting;
	int		g_iLightCount; // Number of lights enabled
	float 	gTime;
	int passEnum;
	uint pickId,drawFlag;
	float clipY; int isOIT;
	//custom param

	int iv, iv1, iv2, iv3;
	float fv, fv1, fv2, fv3;
	vec4 fvecs[10];
};

// adding function to calculate lightning
ColorsOutput CalcLighting2( vec3 worldNormal, vec3 worldPos, vec3 cameraPos, vec4 vertexColour)
{
   ColorsOutput ret;
   ret.Diffuse=vec4(0,0,0,0);
#if USE_SPECULAR
	vec3 toEye     = normalize(g_eyePos - worldPos);
	ret.Specular=vec4(0,0,0,0);
#endif

	//[unroll(2)]
	//[loop]
	for (int i=0;i<g_iLightCount;i++) 
	{
		
  		vec3 toLight = g_lights[i].Position.xyz - worldPos; 
  		float lightDist = length( toLight );
  		float fAtten =1.0;//  1/max(0.1,dot( g_lights[i].Atten.xyz, vec3(1,0,0) ));  //arm gpu div 0 err
  		vec3 lightDir = toLight/lightDist;
   		vec3 _Ambient = g_lights[i].Ambient.rgb * g_material.Ambient.rgb;
  		vec3 _Diffuse = g_lights[i].Diffuse.rgb * g_material.Diffuse.rgb * vertexColour.rgb ;

  		ret.Diffuse.rgb += max(vec3(0,0,0),(dot( lightDir, worldNormal )) /* *fAtten*/ * _Diffuse)*(1-_Ambient)    + _Ambient*_Diffuse ;
#if USE_SPECULAR
  		vec3 _Specular = g_lights[i].Specular.rgb * g_material.Specular.rgb;
#endif
#if 0//Phong
  		vec3 reflection = reflect( - lightDir,worldNormal );
		ret.Specular.a=pow( max(0.f, dot(normalize(reflection), toEye) ), g_material.Specular.a*256.f  ) ;
  		ret.Specular.rgb += max(vec3(0,0,0),ret.Specular.a* _Specular/* *fAtten*/ );
		
#elif USE_SPECULAR	//Blinn Phong
  		vec3 halfAngle = normalize(( lightDir+toEye )/2.f);
		float sp=pow(  max(0.f,dot(halfAngle, worldNormal)), g_material.Specular.a*256.f );
  		ret.Specular.rgb += max(vec3(0,0,0),sp * _Specular /* *fAtten*/  );
	ret.Specular.a=0;

#else
	
#endif
	
	} 
	ret.Diffuse += g_material.Emissive;// +g_eyePos.xyzz;
	//ret.Specular=saturate(ret.Specular);
	 ret.Specular= clamp(ret.Specular,0.0,1.0);
	return ret;
}