#version 450

struct light_t
{
    vec4 Diffuse;
    vec4 Specular;
    vec4 Ambient;
    vec4 Position;
    vec4 Direction;
    uint Type;
    float Range;
    float Falloff;
    float Attenuation0;
    float Attenuation1;
    float Attenuation2;
    float Theta;
    float Phi;
};

layout(constant_id = 1229) const uint vertex_fog_mode = 0u;
layout(constant_id = 1228) const bool fog_enabled = false;
layout(constant_id = 1231) const uint point_mode = 0u;

layout(set = 0, binding = 0, std140) uniform D3D9FixedFunctionVS
{
    layout(row_major) mat4 WorldView;
    layout(row_major) mat4 Normal;
    layout(row_major) mat4 InverseView;
    layout(row_major) mat4 Projection;
    layout(row_major) mat4 TexcoordTransform0;
    layout(row_major) mat4 TexcoordTransform1;
    layout(row_major) mat4 TexcoordTransform2;
    layout(row_major) mat4 TexcoordTransform3;
    layout(row_major) mat4 TexcoordTransform4;
    layout(row_major) mat4 TexcoordTransform5;
    layout(row_major) mat4 TexcoordTransform6;
    layout(row_major) mat4 TexcoordTransform7;
    vec4 ViewportInfo_InverseOffset;
    vec4 ViewportInfo_InverseExtent;
    vec4 GlobalAmbient;
    light_t Light0;
    light_t Light1;
    light_t Light2;
    light_t Light3;
    light_t Light4;
    light_t Light5;
    light_t Light6;
    light_t Light7;
    vec4 Material_Diffuse;
    vec4 Material_Ambient;
    vec4 Material_Specular;
    vec4 Material_Emissive;
    float Material_Power;
    float TweenFactor;
} consts;

layout(push_constant, std430) uniform render_state_t
{
    vec3 fog_color;
    float fog_scale;
    float fog_end;
    float fog_density;
    float alpha_ref;
    float point_size;
    float point_size_min;
    float point_size_max;
    float point_scale_a;
    float point_scale_b;
    float point_scale_c;
} render_state;

layout(location = 0) in vec4 in_Position0;
layout(location = 1) in vec4 in_Normal0;
layout(location = 4) in vec4 in_Texcoord0;
layout(location = 5) in vec4 in_Texcoord1;
layout(location = 6) in vec4 in_Texcoord2;
layout(location = 7) in vec4 in_Texcoord3;
layout(location = 8) in vec4 in_Texcoord4;
layout(location = 9) in vec4 in_Texcoord5;
layout(location = 10) in vec4 in_Texcoord6;
layout(location = 11) in vec4 in_Texcoord7;
layout(location = 7) out vec4 out_Normal0;
layout(location = 0) out vec4 out_Texcoord0;
layout(location = 1) out vec4 out_Texcoord1;
layout(location = 2) out vec4 out_Texcoord2;
layout(location = 3) out vec4 out_Texcoord3;
layout(location = 4) out vec4 out_Texcoord4;
layout(location = 8) out vec4 out_Texcoord5;
layout(location = 9) out vec4 out_Texcoord6;
layout(location = 10) out vec4 out_Texcoord7;
layout(location = 5) out vec4 out_Color0;
layout(location = 6) out vec4 out_Color1;
layout(location = 11) out float out_Fog0;
float _251;

void main()
{
    vec4 _129 = in_Position0 * consts.WorldView;
    vec3 _137 = mat3(consts.Normal[0].xyz, consts.Normal[1].xyz, consts.Normal[2].xyz) * in_Normal0.xyz;
    gl_Position = _129 * consts.Projection;
    vec4 _142 = vec4(_137, 1.0);
    out_Normal0 = _142;
    out_Texcoord0 = _142 * consts.TexcoordTransform0;
    out_Texcoord1 = in_Texcoord0;
    out_Texcoord2 = _142 * consts.TexcoordTransform2;
    out_Texcoord3 = in_Texcoord3;
    out_Texcoord4 = in_Texcoord4;
    out_Texcoord5 = in_Texcoord5;
    out_Texcoord6 = in_Texcoord6;
    out_Texcoord7 = in_Texcoord7;
    bool _180 = consts.Light0.Type == 3u;
    vec3 _182 = _129.xyz;
    vec3 _185 = consts.Light0.Position.xyz - _182;
    float _186 = length(_185);
    vec3 _189 = normalize(mix(_185, -consts.Light0.Direction.xyz, bvec3(_180)));
    float _192 = 1.0 / fma(_186, fma(_186, consts.Light0.Attenuation2, consts.Light0.Attenuation1), consts.Light0.Attenuation0);
    float _197 = _180 ? 1.0 : ((_186 > consts.Light0.Range) ? 0.0 : (isnan(3.4028234663852885981170418348452e+38) ? _192 : (isnan(_192) ? 3.4028234663852885981170418348452e+38 : min(_192, 3.4028234663852885981170418348452e+38))));
    float _199 = dot(-_189, consts.Light0.Direction.xyz);
    float _210 = (consts.Light0.Type == 2u) ? (_197 * clamp((_199 <= consts.Light0.Theta) ? ((_199 > consts.Light0.Phi) ? pow((_199 - consts.Light0.Phi) / (consts.Light0.Theta - consts.Light0.Phi), consts.Light0.Falloff) : 0.0) : 1.0, 0.0, 1.0)) : _197;
    float _218 = clamp(dot(_137, normalize(_189 - normalize(_182))), 0.0, 1.0);
    vec4 _231 = fma(consts.Material_Diffuse, vec4(0.0) + (consts.Light0.Diffuse * (clamp(dot(_137, _189), 0.0, 1.0) * _210)), fma(consts.Material_Ambient, vec4(0.0) + (consts.Light0.Ambient * _210), fma(consts.Material_Ambient, consts.GlobalAmbient, consts.Material_Emissive)));
    out_Color0 = clamp(vec4(_231.x, _231.y, _231.z, consts.Material_Diffuse.w), vec4(0.0), vec4(1.0));
    out_Color1 = clamp(consts.Material_Specular * (vec4(0.0) + (consts.Light0.Specular * ((_218 > 0.0) ? (pow(_218, consts.Material_Power) * _210) : 0.0))), vec4(0.0), vec4(1.0));
    _251 = 0.0;
    if (fog_enabled)
    {
        float _254 = abs(_129.z);
        float _270;
        switch (vertex_fog_mode)
        {
            case 1u:
            {
                _270 = exp(-(_254 * render_state.fog_density));
                break;
            }
            case 2u:
            {
                float _263 = _254 * render_state.fog_density;
                _270 = exp(-(_263 * _263));
                break;
            }
            case 3u:
            {
                float _268 = (render_state.fog_end - _254) * render_state.fog_scale;
                float _305 = isnan(0.0) ? _268 : (isnan(_268) ? 0.0 : max(_268, 0.0));
                _270 = isnan(1.0) ? _305 : (isnan(_305) ? 1.0 : min(_305, 1.0));
                break;
            }
            default:
            {
                _270 = 1.0;
                break;
            }
        }
        _251 = _270;
    }
    out_Fog0 = _251;
    vec3 _286 = _129.xyz;
    float _287 = dot(_286, _286);
    gl_PointSize = clamp((bitfieldExtract(point_mode, 0, 1) == 1u) ? (render_state.point_size / sqrt(render_state.point_scale_a + fma(render_state.point_scale_b, sqrt(_287), render_state.point_scale_c * _287))) : render_state.point_size, render_state.point_size_min, render_state.point_size_max);
}

