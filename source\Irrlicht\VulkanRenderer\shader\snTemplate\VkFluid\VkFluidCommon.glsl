// VkFluid Common Definitions
#include "VkFluidSharedHeader.h"
#define COVARIANCE_COMPUTATION  0

#define MTR_A2D         1
#define RENDER_DIFFUSE  1

// Shared struct definitions for all VkFluid shaders
// Common uniform structure for fluid parameters
struct VkFluidVolumeUniforms {

        uint volumeResX ;
        uint volumeResY ; 
        uint volumeResZ ;
        float pad0; // Padding for alignment

    vec3 volumeMin;
    float time;
    vec3 volumeMax;
    float rayStepSize;
    vec3 volumeSize;
    float densityThreshold;
    vec4 baseColor;
    vec3 absorb;
    int renderDiffuse;
    uint numParticles;
    uint maxRaymarchSteps;
    float gridCellSize;        // Grid cell size in world units
    float pad1;
    vec3 lightDir;
    float kernelRadius ;
    
    float kernelRadiusSq;      // Pre-computed kernelRadius * kernelRadius for performance
    float traceMaxDensity;
    float pad2,pad3;                // Padding for alignment
    

    vec4 pm1;
    vec4 pm2;
    vec4 mtrColor[32];
 
 };

// For compute shaders: use as direct uniform buffer
#ifdef COMPUTE_SHADER
layout(binding = 0) uniform ComputeUniforms {
    VkFluidVolumeUniforms uniforms;
};
#endif
