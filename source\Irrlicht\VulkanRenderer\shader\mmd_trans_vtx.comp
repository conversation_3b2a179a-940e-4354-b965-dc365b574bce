#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
#include "dlCodes/vr/quat.glsl"
#include "mmd_0_header.glsl"

 

#if 1
layout(binding = 0) uniform  TransformMats //
{ 
	int vtxCount, nodeCount, test1, test2;
	vec4 pad, pad2, pad3;
    mat4 trans[511]; 
};
#else
layout(std430, binding = 0) readonly buffer  TransformMats //
{ 
    mat4 trans[]; 
};
#endif

layout(std430, binding = 1) readonly buffer  PosNormIn //
{ 
    PosNormS pni[]; 
};

struct  UnBInfoS
{
    int	m_skinningType, p1, p2, p3;
    int	m_boneIndex[4];
    float	m_boneWeight[4];
    float	pad[4];
    float	pad1[4];
};

struct  UnSInfoS
{
    int	m_skinningType, pd1, pd2, pd3;
    int	m_boneIndex[2];
    float	m_boneWeight,p0;

    vec3	m_sdefC;    float p1;
    vec3	m_sdefR0;   float p2;
    vec3	m_sdefR1;   float p3;
} ;


layout(std430, binding = 2) readonly buffer  UnSInfo //
{ 
    UnSInfoS uns[]; 
};
layout(std430, binding = 2) readonly buffer  UnBInfo
{ 
    UnBInfoS unb[];
};
layout(std430, binding = 3) readonly buffer  PosMorph //
{ 
    vec4 posMph[]; 
};
 

layout(std430, binding = 5) buffer  PosNormOut //
{ 
    PosNormS pno[]; 
};			
		 

layout (local_size_x = 256) in;
 
 ///---------------------
// Quaternion structure

 
vec4 q_blend(vec4 q1, float w1, vec4 q2, float w2) {
	if(dot(q1, q2) < 0) {
		q2 = -q2;
	}
	
	return normalize(w1*q1 + w2*q2);
}
 
 
void main() 
{
 
	// Current SSBO index
	uint idx = gl_GlobalInvocationID.x;
    if (idx>vtxCount) return;
	mat4 m=mat4(1);
    pno[idx].u=pni[idx].u;
    pno[idx].v=pni[idx].v;
    pno[idx].dtPos = pno[idx].pos;
    UnBInfoS v=unb[idx];
    switch (v.m_skinningType)
    {
        case 0://weight1
        {
             m =trans[v.m_boneIndex[0]];
        }
        break;
        case 1://weight2
        {
            mat4 m0=trans[v.m_boneIndex[0]];
            mat4 m1=trans[v.m_boneIndex[1]];
            m = m0*v.m_boneWeight[0] + m1 * v.m_boneWeight[1];
        }
        break;
        case 2://weight4
        {
            mat4 m0=trans[v.m_boneIndex[0]];
            mat4 m1=trans[v.m_boneIndex[1]];
            mat4 m2=trans[v.m_boneIndex[2]];
            mat4 m3=trans[v.m_boneIndex[3]]; 
            m = m0*v.m_boneWeight[0] + m1 * v.m_boneWeight[1]+ m2 * v.m_boneWeight[2]+ m3 * v.m_boneWeight[3];          
        }
        break;
        case 3://SDEF
        {
            UnSInfoS v=uns[idx];
            mat4 m0=trans[v.m_boneIndex[0]];
            mat4 m1=trans[v.m_boneIndex[1]];
            float w0=v.m_boneWeight;
            float w1=1-w0;
            vec3 center=v.m_sdefC;
            vec3 cr0=v.m_sdefR0; 
            vec3 cr1=v.m_sdefR1;
            quat q0=make_quat2(mat3(m0));
            quat q1=make_quat2(mat3(m1));
            vec3 pos= pni[idx].pos.xyz+posMph[idx].xyz;
            mat3 rmt= make_mat3(slerp(q0,q1,w1));
#if 1
            pno[idx].pos.xyz =  rmt * (pos-center) 
            + vec3(m0*vec4(cr0,1))*w0
            + vec3(m1*vec4(cr1,1))*w1;
           
#else
            mat4 m = w0 * m0 + w1 *m1;
            pno[idx].pos.xyz = vec3(m*vec4(center,1)
            + vec4(rmt*(pos-center),0));

#endif
            pno[idx].nor.xyz =rmt* pni[idx].nor.xyz;
            return;   
        }
        break;
    }



    pno[idx].pos= (m * vec4(pni[idx].pos+posMph[idx].xyz,1)).xyz ;
    pno[idx].nor= (mat3(m) * pni[idx].nor);
    vec3 dt=pno[idx].pos-pno[idx].dtPos;
    pno[idx].dtPos=dt ;
    pno[idx].velLen = (pno[idx].velLen*0.87+length(dt)*60.0);
}

