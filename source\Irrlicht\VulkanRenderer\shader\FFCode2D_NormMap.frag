#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;

layout (binding = 0) uniform UBO 
{

				float colorMul;
				float intenMul;
				float specFactor;
				float specPow;

				int colorMode, i01, i02, i03;

				vec3 lightDir;
				float heightBase;

				vec4 pad[15 - 3];


	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w

} ubo;

layout (binding = 2)  uniform sampler2D g_tex0_sampler;
layout (binding = 3)  uniform sampler2D g_tex1_sampler;

layout(location = 0) in vec4 i_colorD;
layout(location = 1) in vec2 i_tex0;

//========================================= PS ========================================
layout (location = 0) out vec4 outFragColor;


void main()
{

	vec4 c0 =  texture(g_tex0_sampler,i_tex0); 

	vec4 c1 =  texture(g_tex1_sampler,i_tex0); 

#if 1
	const float weight[9]={0.5,1,0.5, 1,2,1,0.5,1,0.5};

	
	float tx=i_tex0.x,ty=i_tex0.y;
	vec2 divRes=1.0f/ ubo.res;
	
	vec2 sumNorm =vec2(0.0f);
	int ixy=0;
	for (int y=-1; y<=1;y++)for (int x=-1;x<=1;x++)
	{
	vec2 cad = vec2(x,y) * divRes;
	
	//vec2 h1=texture( g_tex1_sampler, vec2(tx-dx,ty)).ra;
	float dx=texture( g_tex1_sampler, i_tex0+cad+vec2(divRes.x,0)).r;
	float dy=texture( g_tex1_sampler, i_tex0+cad+vec2(0,divRes.y)).r;
	
	//vec2 v2=texture( g_tex1_sampler, vec2(tx,ty-dy)).ra;

	//float dh=(h1.x+h1.y-h2.x-h2.y);	float dv=(v1.x+v1.y-v2.x-v2.y);


	float dh=((c1.r-dx));	float dv=  ((c1.r-dy));
	sumNorm.x+=dh * weight[ixy]; sumNorm.y+=dv * weight[ixy];
	ixy++;
	}
	sumNorm/=8.f;
	vec3 bumpNormal= normalize(vec3((sumNorm.x),(sumNorm.y), -0.1f ));


#else	
	float tx=i_tex0.x,ty=i_tex0.y;
	
	vec2 tex_offset = 0.5f / vec2(textureSize(g_tex0_sampler, 0)) ;
	
	float dx=tex_offset.x, dy=tex_offset.y;
	//vec2 h1=texture( g_tex1_sampler, vec2(tx-dx,ty)).ra;
	vec2 h2=texture( g_tex1_sampler, vec2(tx+dx,ty)).ra;
	vec2 v1=texture( g_tex1_sampler, vec2(tx,ty+dy)).ra;
	//vec2 v2=texture( g_tex1_sampler, vec2(tx,ty-dy)).ra;

	//float dh=(h1.x+h1.y-h2.x-h2.y);	float dv=(v1.x+v1.y-v2.x-v2.y);

#if 0
	float dh=atan((h1.x-h2.x));	float dv=  atan((v1.x-v2.x));
	vec3 bumpNormal= normalize(vec3(sin(dh),sin(dv),-0.2));
#else
	float dh=((c1.r-h2.x));	float dv=  ((c1.r-v1.x));

	vec3 bumpNormal= normalize(vec3((dh),(dv), -0.1f ));

#endif
#endif

	vec3 viewDir =vec3(0,0,-1);
	vec3 lightVector1 = (ubo.lightDir);
	
	float lightIntensity = max(0.f,dot(bumpNormal, -lightVector1));
	vec3	reflection = //reflect(viewDir,  bumpNormal);   
		vec3(0,0,-1) + 2.f * bumpNormal * bumpNormal.z;
	float specular = pow(max(0.f,dot(reflection, lightVector1)),ubo.specPow)*ubo.specFactor; 
	vec4 c;

#if 1
	//c.rgb= (vec3(1,0,0)-c1.g*		(1.f-c1.rrr)		)		+		specular*c1.r; 

	c.rgb= (i_colorD.rgb*((1.f-ubo.colorMul)+ubo.colorMul*vec3(lightIntensity))
		 + ubo.intenMul*vec3(lightIntensity))*c0.rgb
	 +vec3(specular) * c1.a;// step(0.000001,c1.a)
	 ; 

	c.a=c0.a;//*v1.y*v2.y*h1.y*h2.y;
#else
	c.rgb=c0.rgb;
	c.a=c1.a;
#endif
	outFragColor= c;
}