#define PI  3.1415927
#define PI2 6.2831853

struct Ember {
	int emc;		//EmbCount	n>=1000: per ms  ,  n<1000: real count
	int	reb;		//Rebirth
	int	tt;
	int tt1;

	float life;	//life	
	float life1;	//second life	
	float spc;	// speed base
	float spr;	// speed Power

	float r;			//radius
	float m;   //m x g 
	float decml;
	uint mpr;			//turning type idx

	uint mpc;
	int	texId;
	int gf;
	uint tflag;

	uint cflag;
	uint gmode;
	int tt2;
	uint pflag;

	uint mf;
	uint stage; 
	float c2skr;		//conv2 skip ratio
	float c2spd;		//conv2 c2skr- length(vel)*c2spd 

	uint soundId;
	float soundVel,soundAdd;
	float c1rat;  //conv1 ratio

	vec4 fCol, fCol2;
	vec4 fv,fv1,lv;
	vec4 cv;
	uvec4 uiv; //fv:movto pm fv1:movtoG pm
	
	vec2 sst;//set stop timer
	float ocm;  // oit c mul
	int mid;
	uvec4 pad2;
	//total 16 vec4 size
};

// struct GenFirework {
// 	int embId;
// 	int forbidCv1;
// 	int mode, bandId;
// 	vec3 pos; float pmz;
// 	vec3 vec; float padv1;
// 	vec4 col;
// 	vec3 tcl; float padv2;
// };

struct VSParticle {
	vec3 pos;         //position of the particle
	float  timer;        //timer for the particle 	
	vec3 vel;           //velocity of the particle
	uint   eid;        //particle type
	vec3  ofs;        //particle type
	int	reb;
	vec4 col;			// CFLAG_Rotate		for rotation particle: xyz=axis  w=rad - not impelmented
	vec3 tcl;		// CFLAG_ImgScatter		x,y: tex center z:tex width (square) 
	float life;
	vec2 pv;
	uint flag;
	uint bid;	// band or stroke id

	
	vec2 stopTimer;
	int	mid;  //matrix id
	float specAdd;
	vec4 pad2;
};

struct VtxOut{
	vec4 outpos;
	vec4 color;
	vec4 text0;
	vec4 fv;
};

layout( binding = 0) readonly uniform  UBO //frame 
{
 	//mat4 mWV;
	//mat4 mVP;
 	mat4 mW;
 	mat4 mV;
 	mat4 mP;
 	mat4 mCustom;
	mat4 mLightV;
	mat4 mLightP;

 	float g_fGlobalTime;
 	float g_fElapsedTime;
 	float randFac;
	float g_fGlobalTimeFMod;

	vec3 g_vFrameGravity;
	float mWScale;
	vec3	launchPos;
	float fFrameDecelerate;
	
	float maxBandV;
	float colorMul;
	float firePow;
	float fireScale;

	vec3 eyePos;
	float  stdDistance;
	vec3 lightPos;
	float padfff;
	int txtMin, txtMax, txtCvt, useShadowMap;

	uint cmdFlag;
	uint cycleStage;
	float CamClipZMin;
	float mainPeakValue;
	
	vec4 landMin;
	vec4 landMax;

	int maxBid_X_;
	uint expireBidMax;
	uint GenCount;
	uint MaxPtcCount;

	int drawIdxCmdKld;
	int vertKldMul;
	int vertKldAdd;
 	int kldCount;

	int downPtrCount, ptrLightCount, canUpdateGen, padi22;
	float eqvAvgPeak,padf1,padf2,padf3;


	
	vec4 pointerLight[FW_MAX_PtrLight];
	vec4 pointerPos[FW_MAX_POINTER];
	vec4 pointerVel[FW_MAX_POINTER];

	vec4 mtpPos[FW_MAX_POINTER];
	
	vec4 bandv[PT_BANDS];


	//uint pcptIds[FW_MAX_PtClouds];
	//GenFirework gf[FW_GEN_MAX];

} ;

struct GlslVkDrawIndexedIndirectCommand {
    uint    indexCount;
    uint    instanceCount;
    uint    firstIndex;
    int     vertexOffset;
    uint    firstInstance;
};

struct GlslVkDrawIndirectCommand {
    uint    vertexCount;
    uint    instanceCount;
    uint    firstVertex;
    uint    firstInstance;
} ;

struct GlslVkDispatchIndirectCommand {
    uint    y;
    uint	x;
    uint    z;
};

struct PtCloundPoint 
{
	vec3 pos;
	float alpha;
};

layout(binding = 1) readonly buffer  cbOnlyOnce //once
{
	Ember Emb[FW_MAX_TYPE_COUNT];
	PtCloundPoint PCPts[FW_MAX_PtCloudPoints];
};


#ifdef IS_CS

layout(std430, binding = 2) buffer  cbPtc 
{   
	 VSParticle Pts[]; 
};

layout(std430, binding = 3) readonly buffer  cbPtcGen
{   
	 VSParticle PtsGen[]; 
};


layout (std430,binding = 5)  buffer CountBuffer 
{
	int showCount,showCountOut,freeCount,totalCount;
	uint freeF,freeB,retCount,kld;
	uint oit_counter, mSubGroupSize, pad2, pad3;
	GlslVkDrawIndexedIndirectCommand drawIdxCmd;
	GlslVkDrawIndirectCommand drawNoIdxCmd;
	GlslVkDispatchIndirectCommand dsipCmd;
	float f0,f1,f2,f3;
	float soundCounts[136];
};


layout (std430,binding = 6)  buffer IdsBuf
{
	uint ids[];
};

layout (std430,binding = 7)  buffer IdsOutBuf
{
	uint idsOut[];
};
layout (std430,binding = 8)  buffer FreeIdsBuf
{
	uint FreeIds[];
};
layout (std430,binding = 9)  buffer VtxOutBuf
{
	VtxOut vtxs[];
};
struct VtxMatStruct{
	mat4 m;
	int  cvt,v1type,landType,spec;
	ivec4  i1;
	vec3 	v0; float v0p;
	vec3 	v1; float v1p;
};
layout (std430,binding = 11)  buffer VtxMatsBuf
{
	VtxMatStruct vtxMats[];
};

#elif defined(IS_VS_PASS)
layout (std430,binding = 9) readonly  buffer VtxOutBuf
{
	VtxOut vtxs[];
};
#elif defined(IS_VS_CSDRAW)
layout(std430, binding = 2) readonly buffer  cbPtc {   //std140 16bytes aligned? check std430 
	 VSParticle PtcBuf[]; 
};


layout (std430,binding = 6) readonly buffer ShowIdsBuffer 
{
	uint ids[];
};
#elif defined(IS_VS_DRAW_POINTLIST)
layout(std430, binding = 2) readonly buffer  cbPtc {   //std140 16bytes aligned? check std430 
	 VSParticle PtcBuf[]; 
};

layout (std430,binding = 6) readonly buffer ShowIdsBuffer 
{
	uint ids[];
};
#endif




float CalcRatio(float ratio, uint mp)
{
	float ret;
	switch (mp & 0xfu)
	{
	case 1: ret = ratio;				break;
	case 2: ret = 1.0f - ratio;		break;
	case 3: ret = 0.5f + 0.5f*ratio;	break;
	case 4: ret = 1.0f - 0.5f*ratio;	break;
	default: ret = 1.0f;				break;
	}
		switch (mp & 0xf0)
		{
			case 0x10:
			ret = sin(ret * PI / 2);
			break;
			case 0x20:
			ret = sin(ret * PI); 		// so 21 == 22
			break;
			case 0x30:
			ret = 1.0f-sin(ret * PI); 		
			break;
		}
	//if ((mp & 0xf0) == 0x10)		ret = sin(ret*PI / 2);

	return ret;
}


// adapted from http://stackoverflow.com/a/26127012/128511
#if 0
vec3 fibonacciSphere(float samples, float i) {
  float rnd = 1.;
  float offset = 2. / samples;
  float increment = PI * (3. - sqrt(5.));

  //  for i in range(samples):
  float y = ((i * offset) - 1.) + (offset / 2.);
  float r = sqrt(1. - pow(y ,2.));

  float phi = mod(i + rnd, samples) * increment;

  float x = cos(phi) * r;
  float z = sin(phi) * r;

  return vec3(x, y, z);
}
#endif
vec3 FibonacciSphere(float num, float i)
{
	float rnd=1.;
	float offset = 2./num;
	const float increment = PI*(3.-sqrt(5.));

	float y=((i*offset)-1.) + (offset/2.);
	float r=sqrt(1. -pow(y,2.));
	float phi=mod(i+rnd,num)*increment;

	float x=cos(phi)*r;
	float z = sin(phi)*r;
	return vec3(x,y,z);
}

/*
mat4 rotation3d(vec3 axis, float angle) //gl matrix: v = M * v
{
  axis = normalize(axis);
  float s = sin(angle);
  float c = cos(angle);
  float oc = 1.0 - c;

  return mat4(
    oc * axis.x * axis.x + c,           oc * axis.x * axis.y - axis.z * s,  oc * axis.z * axis.x + axis.y * s,  0.0,
    oc * axis.x * axis.y + axis.z * s,  oc * axis.y * axis.y + c,           oc * axis.y * axis.z - axis.x * s,  0.0,
    oc * axis.z * axis.x - axis.y * s,  oc * axis.y * axis.z + axis.x * s,  oc * axis.z * axis.z + c,           0.0,
    0.0,                                0.0,                                0.0,                                1.0
  );
}

mat3 rotation3d3(vec3 axis, float angle) //gl matrix: v = M * v
{
  axis = normalize(axis);
  float s = sin(angle);
  float c = cos(angle);
  float oc = 1.0 - c;

  return mat3(
    oc * axis.x * axis.x + c,           oc * axis.x * axis.y - axis.z * s,  oc * axis.z * axis.x + axis.y * s, 
    oc * axis.x * axis.y + axis.z * s,  oc * axis.y * axis.y + c,           oc * axis.y * axis.z - axis.x * s, 
    oc * axis.z * axis.x - axis.y * s,  oc * axis.y * axis.z + axis.x * s,  oc * axis.z * axis.z + c        
   
  );
}
*/