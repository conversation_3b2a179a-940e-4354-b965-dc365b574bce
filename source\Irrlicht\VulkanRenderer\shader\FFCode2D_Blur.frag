#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;
#define PI 3.1415926
layout (binding = 0) uniform UBO 
{


				float blurStep;
				float blurWeightMul;
				float sigma, sigma2;
				int blurSize, addPattern, i02, i03;
				float apLenMul, apHeightMul, apHeightAdd, f13;
				float apf1, apf2, _22, _23;
				vec4 matRtt;
				vec4 pad[15 - 5];

	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w
} ubo;

layout (binding = 2)  uniform sampler2D g_tex0_sampler;




	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;




//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;





float blurknl(in float x, in float sigma)
{
	return 0.39894*exp(-0.5*x*x/(sigma*sigma))/sigma;
}

float blurknl2(in float x,in float y, in float s2)
{
	return 0.15915*exp(-0.5*(x*x + y*y)/s2)/s2;
}
void main( )
{
	vec2 coord= vec2((i_tex0.x - 0.5) * ubo.resWdH, i_tex0.y - 0.5) * mat2(ubo.matRtt.x,ubo.matRtt.y,ubo.matRtt.z,ubo.matRtt.w)*	vec2(ubo.resHdW,1);
		//declare stuff
		int mSize = ubo.blurSize*2+1;
		int kSize = ubo.blurSize;
#if BLUR_COLOR_TO_BW_HMAP
		vec2 final_colour = vec2(0,0);
#else
		vec4 final_colour = vec4(0,0,0,0);
#endif

		//vec2 res=vec2(textureSize(g_tex0_sampler, 0));
#if 0
		float kernel[32];
		//create the 1-D kernel
		float sigma = 7.0;
		float Z = 0.0;
		for (int j = 0; j <= kSize; ++j)
		{
			kernel[kSize+j] = kernel[kSize-j] = blurknl(float(j),  ubo.sigma);
		}
		
		//get the normalization factor (as the gaussian has been clamped)
		for (int j = 0; j < mSize; ++j)
		{
			Z += kernel[j];
		}
#endif
		float s2=float(kSize*kSize)+0.1f;
		//read out the texels
		for (int i=-kSize; i <= kSize; ++i)
		{
			for (int j=-kSize; j <= kSize; ++j)
			{
				float x=float(i);
				float y=float(j);
				if (x*x+y*y<s2)
				{
#if BLUR_COLOR_TO_BW_HMAP
					//final_colour += kernel[kSize+j]*kernel[kSize+i]*texture(g_tex0_sampler, (i_tex0+vec2(x,y)*vec2(ubo.blurStep,ubo.blurStep)/ res) ).a;
					vec4 c=texture(g_tex0_sampler, (i_tex0+ vec2(float(i),float(j))*vec2(ubo.blurStep,ubo.blurStep)/ ubo.res) );
					vec2 a=//c.a;//1-exp(-2*c.a);// 
					vec2(dot(c.rgb, vec3( 0.3 , 0.59 , 0.11 )),c.a);

					final_colour += blurknl2(x,y,ubo.sigma2)					*(a);
#else
					vec4 c=texture(g_tex0_sampler, (i_tex0+ vec2(float(i),float(j))*vec2(ubo.blurStep,ubo.blurStep)/ ubo.res) );
					
					float br=blurknl2(x,y,ubo.sigma2)	;

					final_colour+= c*br;
#endif
				}
			}
		}
#if 0
		vec2 tr= coord*ubo.res/ubo.apLenMul;
		switch (ubo.addPattern)
		{
			case 1:final_colour*= (cos(tr.x*PI*2)*ubo.apf1+cos(tr.y*PI*2)*ubo.apf2)*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 2:final_colour+= (cos(tr.x*PI*2)*ubo.apf1+cos(tr.y*PI*2)*ubo.apf2)*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 3:final_colour*= (abs(fract(tr.x)-0.5)*ubo.apf1+abs(fract(tr.y)-0.5)*ubo.apf2)*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 4:final_colour+= (abs(fract(tr.x)-0.5)*ubo.apf1+abs(fract(tr.y)-0.5)*ubo.apf2)*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 5:final_colour*= (fract(tr.x)*ubo.apf1+fract(tr.y)*ubo.apf2)*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 6:final_colour+= (fract(tr.x)*ubo.apf1+fract(tr.y)*ubo.apf2)*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 7:final_colour*= (fract(tr.x)*fract(tr.y))*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 8:final_colour+= (fract(tr.x)*fract(tr.y))*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 9:final_colour*= ((fract(tr.x)-0.5)*2*cos(tr.x*PI*2)*ubo.apf1+(fract(tr.y)-0.5)*2*cos(tr.y*PI*2)*ubo.apf2)*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 10:final_colour+= (abs(fract(tr.x)-0.5)*cos(tr.x*PI*2)*ubo.apf1+abs(fract(tr.y)-0.5)*cos(tr.y*PI*2)*ubo.apf2)*ubo.apHeightMul+ubo.apHeightAdd;break;
			case 11:final_colour*= (fract(tr.x)*ubo.apf1+fract(tr.y)*ubo.apf2)*ubo.apHeightMul+ubo.apHeightAdd;break;
			
			default:break;
		}
#endif

		#if BLUR_COLOR_TO_BW_HMAP
		outFragColor = final_colour.xxxy * ubo.blurWeightMul;
		#else
		outFragColor = vec4(final_colour.rgb* ubo.blurWeightMul,final_colour.a)*i_colorD;
		#endif
		//outFragColor = vec4( 1,1,1,final_colour*ubo.blurWeightMul);

		
		//vec4 col=texture(g_tex0_sampler, i_tex0);		outFragColor = vec4( col.rgb*col.a*final_colour/(Z*Z)*ubo.blurWeightMul, 1.0);		
		//outFragColor = vec4( ( vec3(1,1,1)-texture(g_tex0_sampler, i_tex0).rgb)*final_colour/(Z*Z)*ubo.blurWeightMul, 1.0);
}