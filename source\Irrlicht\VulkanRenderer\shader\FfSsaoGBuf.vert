#version 460
#extension GL_GOOGLE_include_directive : enable
#include "FFCodeHeader.glsl"
	layout(location = 0) in vec3 i_pos;
	layout(location = 1) in vec3 i_norm;
	layout(location = 2) in vec4 i_color;
	layout(location = 3) in vec2 i_tex0;


layout (location = 0) out vec3 outNormal;
layout (location = 1) out vec2 outUV;
layout (location = 2) out vec3 outColor;
layout (location = 3) out vec3 outPos;

void main() 
{
	vec4 oPos = vec4(i_pos,1) * g_mWorld*g_mView;
	gl_Position = oPos*g_mProj;  //ubo.projection * ubo.view * ubo.model * inPos;
	
	outUV = i_tex0;

	// Vertex position in view space
	outPos = oPos.xyz;

	// Normal in view space
	//vec3 normalMatrix =   mul(ubo.view, ubo.model);
	outNormal =   i_norm * mat3(g_mWorld*g_mView);

	outColor = i_color.rgb;
}
