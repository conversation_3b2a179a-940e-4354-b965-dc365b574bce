#version 460
#extension GL_GOOGLE_include_directive : enable

#ifndef IS_OIT
#define IS_OIT 1 //  0: disabled, 1: linked list (my old method) , 2: interlock method
#endif

precision highp float;

#define USE_SPECULAR 1
#define AR_SHADOW  0
#include "FFCodeHeader.glsl"
// References HLSL
// Flow control: http://msdn.microsoft.com/en-us/library/bb509600(VS.85).aspx
// Semantics: http://msdn.microsoft.com/en-us/library/bb509647(VS.85).aspx

// Constants



#define MAX_LIGHTS 									2
#define EMT_LIGHTMAP								2
#define EMT_LIGHTMAP_ADD							3


layout (binding = 2)  uniform sampler2D g_tex0_sampler;
layout (binding = 3)  uniform sampler2D g_tex1_sampler;




layout(location = 0) in vec2 i_tex0;

layout(location = 2) in vec4 i_colorD;
#if USE_SPECULAR 
layout(location = 1) in vec3 i_wNorm;       //world space normal
layout(location = 3) in vec4 i_colorS;

#endif
//========================================= PS ========================================
layout(location = 4) in vec4 i_wPos;

//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;

#if 1//shadow map


#define ambient 0.5

#ifdef IS_OIT

#if IS_OIT == 2
// Interlock OIT extensions - enable interlock support
#define INTERLOCK_SUPPORTED 1

#if INTERLOCK_SUPPORTED
#ifdef GL_NV_fragment_shader_interlock
#extension GL_NV_fragment_shader_interlock : enable
// Use ordered interlock for consistent results
layout(pixel_interlock_ordered) in;
#define beginInvocationInterlock beginInvocationInterlockNV
#define endInvocationInterlock endInvocationInterlockNV
#elif defined(GL_ARB_fragment_shader_interlock)
#extension GL_ARB_fragment_shader_interlock : enable
// Use ordered interlock for consistent results
layout(pixel_interlock_ordered) in;
#define beginInvocationInterlock beginInvocationInterlockARB
#define endInvocationInterlock endInvocationInterlockARB
#endif

// Interlock OIT bindings - A-buffer as storage buffer
layout (std430, binding = 10) buffer AbufferData {
	uvec4 abufferData[];
};
layout(binding = 8, r32ui) uniform coherent uimage2D imgAux;
layout(binding = 11, r32ui) uniform coherent uimage2D imgAuxDepth;

layout (std430, binding = 9) buffer CountBuffer 
{
	uint oit_counter;
	uint maxCount;  // OIT_LAYERS for interlock
	float minAlpha, pad2;
};
#else
// Fallback to linkedlist bindings when interlock not supported
#include "oit_utils.h"
layout (early_fragment_tests) in;
layout(binding = 8, r32ui) uniform highp uimage2D counterImage;
layout (std430,binding = 9) buffer CountBuffer 
{
	uint oit_counter;
	uint maxCount  ;
	float minAlpha,pad2;
};
layout (std430, binding = 10) writeonly buffer oitData {
	OITData data[];
};
#endif

#else
// LinkedList OIT (IS_OIT == 1)

#include "oit_utils.h"

layout (early_fragment_tests) in;

layout(binding = 8, r32ui) uniform highp uimage2D counterImage;
layout (std430,binding = 9) buffer CountBuffer 
{
	uint oit_counter;
	uint maxCount  ;
	float minAlpha,pad2;
};

layout (std430, binding = 10) writeonly buffer oitData {
	OITData data[];
};

#endif

#else

#endif

float textureProj(vec4 shadowCoord, vec2 off)
{

	float shadow = 1.0;
	if ( shadowCoord.z > -1.0 && shadowCoord.z < 1.0 ) 
	{
		float dist = texture( g_tex1_sampler, (vec2(1,-1)*shadowCoord.st+1)/2 + off ).r;
		if ( shadowCoord.w > 0.0 && dist < shadowCoord.z *0.9999995f ) 
		{
			shadow = ambient;
		}
	}
	return shadow;
}

float filterPCF(vec4 sc)
{
	ivec2 texDim = textureSize(g_tex1_sampler, 0);
 
	float dx = 1.0 / float(texDim.x);
	float dy = 1.0 / float(texDim.y);

	float shadowFactor = 0.0;
	int count = 0;
	int range = 2;
	
	for (int x = -range; x <= range; x++)
	{
		for (int y = -range; y <= range; y++)
		{
			shadowFactor += textureProj(sc, vec2(dx*x, dy*y));
			count++;
		}
	
	}
	return shadowFactor / count;
}
float LinearizeDepth(float depth)
{
  float n = 25.0; // camera z near
  float f = 1000000.0; // camera z far
  float z = depth;
  return (2.0 * n) / (f + n - z * (f - n));	
}

void main()
{

	//if (i_wPos.y<clipY) discard;
	//return float4(1,1,0,1);
	vec4 normalColor;// = float4(0, 0, 0,0);
	vec4 tex1 =  texture(g_tex0_sampler,i_tex0); 
	normalColor = (tex1*i_colorD ) 
#if USE_SPECULAR 	
		+ i_colorS;//i_colorD.a
#endif
		;
	if (g_material.shadowType>0 ) {
		vec4 suv= i_wPos*g_mLightVP;
		if (suv.w>0.0)
		{
			float shadow=   
				// (texture(g_tex1_sampler, (vec2(1,-1)*suv.st/suv.w+1)/2).r<suv.z/suv.w?0.2:1);
				filterPCF(suv / suv.w);
 			if (g_material.shadowType==2  )
				normalColor  = vec4(1,1,1,1-shadow)*i_colorD;  //ground shadow use
			else 
				normalColor.rgb=normalColor.rgb*(0.7+shadow*0.3);
			
		}
		else normalColor=vec4(0,0,0,0);
	}

#ifdef IS_OIT
#if IS_OIT == 2 && INTERLOCK_SUPPORTED
	// Interlock OIT implementation following NVIDIA sample
	// Early alpha test to avoid unnecessary work
	if (normalColor.a < minAlpha) {
		discard;
	}

	// Convert to unpremultiplied sRGB for 8-bit storage (simplified - assume already in right space)
	vec4 sRGBColor = normalColor;

	// Compute index in the A-buffer - match NVIDIA sample exactly
	ivec2 coord = ivec2(gl_FragCoord.xy);
	const int viewSize = 1080 * 1920; // Should come from scene.viewport.z in NVIDIA sample
	const int sampleID = 0; // For MSAA support, this would come from gl_SampleID
	const int listPos = viewSize * int(maxCount) * sampleID + (coord.y * 1080 + coord.x);

	// Pack color and depth for storage - match NVIDIA sample
	uvec4 storeValue = uvec4(packUnorm4x8(sRGBColor), floatBitsToUint(gl_FragCoord.z), 0xFFFFFFFF, 0);

	// Begin critical section for interlock
	beginInvocationInterlock();

	// Early depth test (simplified version of NVIDIA's USE_EARLYDEPTH)
	uint oldDepth = imageLoad(imgAuxDepth, coord).r;
	if(storeValue.y <= oldDepth || oldDepth == 0)
	{
		const uint oldCounter = imageLoad(imgAux, coord).r;
		imageStore(imgAux, coord, uvec4(oldCounter + 1));

		if(oldCounter < maxCount)
		{
			// Insert fragment directly into A-buffer - match NVIDIA indexing
			abufferData[listPos + int(oldCounter) * viewSize] = storeValue;
			// Inserted, so we won't tail-blend it
			normalColor = vec4(0);
		}
		else
		{
			// Find the furthest element - match NVIDIA sample logic
			int furthest = 0;
			uint maxDepthFound = 0;

			for(int i = 0; i < int(maxCount); i++)
			{
				const uint testDepth = abufferData[listPos + i * viewSize].g;
				if(testDepth > maxDepthFound)
				{
					maxDepthFound = testDepth;
					furthest = i;
				}
			}

			if(maxDepthFound > storeValue.g)
			{
				// Replace the furthest fragment, tail-blending it, with this fragment
				normalColor = unpackUnorm4x8(abufferData[listPos + furthest * viewSize].r);
				abufferData[listPos + furthest * viewSize] = storeValue;
				// Update depth buffer
				imageStore(imgAuxDepth, coord, uvec4(maxDepthFound));
			}
			// else: our fragment is further away, just tail-blend it
		}
	}
	// else: fragment failed early depth test, just tail-blend it

	// End critical section for interlock
	endInvocationInterlock();

	// Output the tail-blended color (premultiplied for blending)
	outFragColor = vec4(normalColor.rgb * normalColor.a, normalColor.a);
	
#else
	// LinkedList OIT Method (IS_OIT == 1) or fallback
	if (iv==1)
	{
		// Early alpha test to avoid unnecessary work
		if (normalColor.a < minAlpha) { discard; return; }
		
		// Only check buffer space if we're going to use it
		if (oit_counter > OIT_BufSize - 1024) { discard; return; }

		// Single atomic operation
		uint idx = atomicAdd(oit_counter, 1);
		
		if (idx < OIT_BufSize) {
			// Pre-multiply alpha for better blending
			#if !PARAM_OIT
			normalColor.rgb *= normalColor.a;
			#endif
			
			ivec2 coord = ivec2(gl_FragCoord.xy);
			uint prev = imageAtomicExchange(counterImage, coord, idx);
			
			#if USE_UINT_COLOR
			// More efficient color packing
			uint color = (uint(normalColor.a * 255.0) << 24)
					   | (uint(normalColor.b * 255.0) << 16)
					   | (uint(normalColor.g * 255.0) << 8)
					   | uint(normalColor.r * 255.0);
			data[idx].color = color;
			#else
			data[idx].color = normalColor;
			#endif
			
			data[idx].depth = gl_FragCoord.z;
			data[idx].prev = prev;
		}
		discard;
	}
	else
		outFragColor = normalColor;
#endif
#else	
	outFragColor = normalColor;
#endif

}










#else //BACK before shadow map
void main()
{
	if (i_wPos.y<clipY) discard;
	//return float4(1,1,0,1);
	vec4 normalColor;// = float4(0, 0, 0,0);
	vec4 tex1 =  texture(g_tex0_sampler,i_tex0); 
	normalColor = (tex1*i_colorD ) 
#if USE_SPECULAR 	
		+ i_colorS
#endif
		;

	outFragColor = normalColor;
}
#endif