#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
#extension GL_ARB_explicit_attrib_location : require
#extension GL_ARB_shader_atomic_counters : require
#extension GL_ARB_shader_image_load_store : require
#extension GL_ARB_shader_storage_buffer_object : require


#include "oit_utils.h"


layout (early_fragment_tests) in;

layout(binding = 1, r32ui) uniform highp uimage2D counterImage;
//layout (offset = 0, binding = 0) uniform atomic_uint counter;

layout (std430,binding = 5)  buffer CountBuffer 
{
	uint counter;
};
coherent layout (std430, binding = 0) buffer oitData {
	OITData data[];
};

layout(binding = 1) readonly buffer  cbOnlyOnce //once
{
  uint bufSize;
};


layout(location = 0) in vec3 colorFrag;

void main(void)
{
	uint idx = atomicAdd(counter,1) ;

	if (idx < bufSize) {
		ivec2 coord = ivec2(gl_FragCoord.xy);
		uint prev = imageAtomicExchange(counterImage, coord, idx);
		uvec3 colorTemp = uvec3(colorFrag * 255.0);
		//uint alpha = uint(0.75 * 255);
		uint color = 0xCC000000 | (colorTemp.r << 16) | (colorTemp.g << 8) | colorTemp.b;
		data[idx].color = color;
		data[idx].depth = gl_FragCoord.z;
		data[idx].prev = prev;
	}
}
