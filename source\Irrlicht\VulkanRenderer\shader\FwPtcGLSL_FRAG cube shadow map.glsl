
#include "GsParticleShared.h"
#define PI  3.1415927
#define PI2 6.2831853
#include "FwParticleShaderShared.glsl"
#include "FwPtcDistanceFunctions.glsl"
#define CALC_TEX 1
#define CUBEMAP_LIGHT 0


layout(location = 0) in	vec4 color;
layout(location = 1) in	vec4 tex;// all interpolation
layout(location = 2) in	vec4 fv;// all interpolation
layout (binding = 3) uniform sampler2DArray samplerArray;


#if FW_USE_SHADOWMAP
layout(location = 3) in	vec4 texDepth;// shadow map 
layout (binding = 4) uniform sampler2D samplerDepth;
#endif




#if !FW_OIT

layout(location = 0) out vec4 outFragColor;

#else

#include "oit_utils.h"


layout (early_fragment_tests) in;

layout(binding = 7, r32ui) uniform highp uimage2D counterImage;
//layout (offset = 0, binding = 0) uniform atomic_uint counter;
layout (std430,binding = 8) buffer CountBuffer 
{
	uint oit_counter;
	uint maxCount;   
	float minAlpha,pad;
	uint viewW;
	uint viewH;
	uint viewSize;
	uint pad2;
};

//coherent 
layout (std430, binding = 10) writeonly buffer oitData {
	OITData data[];
};

#endif

#if FW_USE_SHADOWMAP
#define ambient 0.1
float LinearizeDepth(float depth)
{
  float n = 1.0; // camera z near
  float f = 10.0; // camera z far
  float z = depth;
  return (2.0 * n) / (f + n - z * (f - n));	
}
float textureProj(vec4 shadowCoord, vec2 off)
{
	float shadow = 1.0;
	if ( shadowCoord.z > -1.0 && shadowCoord.z < 1.0 ) 
	{
		float dist = texture( samplerDepth, (vec2(1,-1)*shadowCoord.st+1)/2 + off ).r;
		if ( shadowCoord.w > 0.0 && dist < shadowCoord.z ) 
		{
			shadow = ambient;
		}
	}
	return shadow;
}

float filterPCF(vec4 sc)
{
	ivec2 texDim = textureSize(samplerDepth, 0);
	float scale = 1.5;
	float dx = scale * 1.0 / float(texDim.x);
	float dy = scale * 1.0 / float(texDim.y);

	float shadowFactor = 0.0;
	int count = 0;
	int range = 1;
	
	for (int x = -range; x <= range; x++)
	{
		for (int y = -range; y <= range; y++)
		{
			shadowFactor += textureProj(sc, vec2(dx*x, dy*y));
			count++;
		}
	
	}
	return shadowFactor / count;
}
#endif

void main() 
{
#if FW_OIT
	 vec4 outFragColor;
#endif

#if CALC_TEX 
#if 0
	if (tex.z<250)
	{
		vec4 c = texture(samplerArray, tex.xyz);
		outFragColor = 
		color*vec4(1,1,1,c.r) + c.a*color.a;//  		
		//color*c.r + c.a*color.a;//  		
		
		//vec4(c.rgb,tex.a*c.r)*color + c.a*color.a;
	}
	else
#endif
	{
		
		//vec4 cc = texture(samplerArray, vec3(tex.xy,tex.z-256));		outFragColor = vec4(cc.rgb,tex.a*cc.r)*color + cc.a*color.a;return;
		vec3 rgb=vec3(1,1,1);
		float c,ca=0.0;
		int id= int(tex.z-255.0+0.5);
		vec2 pos=tex.xy*2.0-vec2(1.0);
#if 0
	c=0.5;
#else

		switch(id)
		{
			case 1:
			default://case 1 = 256
			{
				float r=length(pos);
				c=cos(r*PI/2);//*gl_FragCoord.z;
			}
			break;
			case 0://255
			{
				//c=1;
				float r=length(pos);
#if CUBEMAP_LIGHT
				vec3 norm= normalize(vec3(pos.x,pos.y,-sqrt(1-pos.x*pos.x-pos.y*pos.y)));
		 		vec3 reflection =  //vec3(0,0,-1) + 2.f * 			 bumpNormal  ;
			 	reflect( normalize(vec3(eyePos.xy,-eyePos.z))  ,norm );
  		 		ca = max(0, pow( ( dot(normalize(reflection), vec3(0,0,-1) ) ),5));
#endif
				c=smoothstep(0.0,0.1,1-r);
			}
			break;
			case 11://266
			{
				float r=clamp(1-length(pos),0,1); 
				c=r*r;
	
			}	
			break;
			case 12://267
			{

				c=clamp(exp(-12*(abs(length(pos))-0.1)),0,1); 	
				//c=max(exp(-12*(abs(length(pos))-0.2)),0); 		
				rgb=vec3(0,0,0);
			}	
			break;
			case 2://*
			{				
				float r=length(pos); 
				float a=atan(pos.y,pos.x);//+fv.x*fv.z;
				float f=abs(cos(a*(8/2.0)))*.5+.5;//(0.5+0.5*sin(r*PI*8)*(pow(abs(cos(a*5)),8)));
				
				if (r>f) discard;

				c=  1.0 -
				//step(f,r)
				smoothstep(f-0.95,f,r)
				 //f
				 ;

				//c *= 0.5 + 0.5*cos(r*20);
						ca= smoothstep(0.0,1,(sin(PI*32*c+fv.x*5)-0.5 )*c);
				//ca=  f;
				//((1.0 - smoothstep(0.0,0.27,r))) ;
			}
			break;
			
			case 3://*
			{
				
				float r=length(pos); 
				float a=atan(pos.y,pos.x)+fv.x*fv.w;
				float f=(
				abs(fract(5*(a/PI2))-0.5)*2
				)
				*0.5*fv.x
				+0.5
				;
				
				//if (r>f) discard;
				c=  1.0 -
				smoothstep(0,f,r)
				// f
				 ;


				//c=sin(c*PI2);
				//c *= 0.5 + 0.5*cos(r*20);
				//	ca= smoothstep(0.0,1,sin(PI*2*fv.x)*c);
				ca= smoothstep(0.0,1,sin(PI*6*c+fv.x*PI2)*c*0.5);
			}
			break;
			case 4://*
			{
				
				float r=length(pos); 
				float a=atan(pos.y,pos.x)+fv.w;
				float f=(
				abs(fract(5*(a/PI2))-0.5)*2
				)
				*0.5
				+0.5
				;
				
				//if (r>f) discard;
				c=  1.0 -
				smoothstep(0,f,r)
				// f
				 ;


				//c=sin(c*PI2);
				//c *= 0.5 + 0.5*cos(r*20);
				//	ca= smoothstep(0.0,1,sin(PI*2*fv.x)*c);
ca= smoothstep(0.0,1,sin(PI*6*c+fv.x*PI2)*c*0.5);
			}
			break;
			case 5://flower
			{

				//float r=length(pos)*2.0;

				float d=(dfStar(pos,0.5,5,4+  200 *(5*2.0-4)));
				d=d - (0.5-0.02);
				c = (1.0) - sign(d)*(0.5);
				c *= 1.05 - exp(2*(d));
				c *= 0.6 + 0.3*cos(PI*10.0*d);
				//c= mix( c, (1.0), 1.0-smoothstep(0.0,0.02,abs(d)) );

			}
			break;	
						break;
			// case 6:
			// {


			// 	float d=(dfStar(pos,0.5,5,4+1*(5*2.0-4)));
			// 	d=d - (0.5*fv.x-0.02);
			// 	c = (1.0) - sign(d);
			// 	c *= 1.05 - exp(2*(d));
			// 	//c *= 0.6 + 0.3*cos(PI*1.0*d);
				
			// }
			// break;
			case 8://heart
			{
				vec2 p=vec2(pos.x,-pos.y);
				p *=0.52;
				p.y= 0.057 + p.y*1.25 - abs(p.x) * (1.0-abs(p.x));
				float r=length(p);

				c= smoothstep(0,0.3, (0.5-0.02-r)); 
				ca= smoothstep(0.0,.5,sin(PI*12*r+fv.y*PI2)*c*.2);
				
			}
			break;		
			// case 17://   6 sides
			// {

			// 	float d=df6Side(pos,0.5);

			// 	//d=d - (0.5*fv.x);
			// 	c = 1.0- smoothstep(0,fv.x*.5,d);
			// 	//c = borderDf((d), 0.5*fv.x);
			// 	//c= mix( 0, (1.0), 1.0-smoothstep(0.0,0.2,abs(d)) );
			// }
			// break;

			break;


		}
#endif


#if FW_USE_SHADOWMAP
		float shadow=1.0;
		if ( texDepth.w > 0.0) 
		{
			shadow= //(texture(samplerDepth, (vec2(1,-1)*texDepth.st/texDepth.w+1)/2).r<texDepth.z/texDepth.w?0.5:1);
			filterPCF(texDepth / texDepth.w);

#if FW_OIT
			outFragColor =   		vec4(1,1,1,c)		*color * shadow		;
#else
			outFragColor =   vec4(rgb,max(0.0,c))*color		* shadow		+ ca*color.a;
			//outFragColor = vec4(shadow);
#endif
		}
		else
		{
	
		//depth image
		if (c<0.1 || c*color.a<0.1) discard;
		outFragColor = vec4(1);
#else


	#if FW_OIT
			outFragColor =   
		#if CUBEMAP_LIGHT		
			vec4(1+ca,1+ca,1+ca,c)		
		#else
			vec4(1,1,1,c)	
		#endif
			*color //+ ca*color.a
			;
	#else	
				//if (c<0.01) discard;//discard may not improve performance
				outFragColor =  
				// vec4(c,c,c,c)*color
				vec4(rgb,max(0.0,c))*color
				// vec4(1,1,1,clamp(c,0,1))*color 
				+ ca*color.a;
	#endif
#endif

#if FW_USE_SHADOWMAP					
		}
#endif
	}

//outFragColor = vec4(c.rgb,c.r)*color + c.a*color.a;
	//	outFragColor=vec4(gl_FragCoord.z);
#else
		vec4 c = texture(samplerArray, tex.xyz);
		outFragColor = vec4(c.rgb,tex.a*c.r)*color + c.a*color.a;

#endif

#if FW_OIT

if (outFragColor.a<minAlpha ||oit_counter>OIT_BufSize-10240) 
		return;

	uint idx = atomicAdd(oit_counter,1) ;

	if (idx < OIT_BufSize) {

		ivec2 coord = ivec2(gl_FragCoord.xy);
		uint prev = imageAtomicExchange(counterImage, coord, idx);

#if !PARAM_OIT
		outFragColor.rgb*=outFragColor.a*colorMul;
#endif
	#if USE_UINT_COLOR
		uvec4 colorTemp = uvec4(outFragColor * 255.0);
		//uint alpha = uint(0.75 * 255);
		uint color =  (colorTemp.a << 24 )
		// 0xFF000000
				|(colorTemp.b << 16) | (colorTemp.g << 8) | colorTemp.r;		
		data[idx].color = color;
	#else
		data[idx].color = outFragColor;
	#endif
		data[idx].depth = gl_FragCoord.z;
		data[idx].prev = prev;
	}

#endif
}


