// From http://www.reddit.com/r/gamedev/comments/2j17wk/a_slightly_faster_bufferless_vertex_shader_trick/

// NOTE: this is in direct3d texture coord space (origin upper left)
// remember to flip y coordinate

//By CeeJay.dk
//License : CC0 - http://creativecommons.org/publicdomain/zero/1.0/

#include "GsParticleShared.h"
#define PARAM_OIT 0
#define PARAM_OIT_P1  1.5
#define IS_OIT_ADDCOLOR 0
//Basic Buffer/Layout-less fullscreen triangle vertex shader
vec2 triangleVertex(in int vertID, out vec2 texcoord)
{
    /*
    //See: https://web.archive.org/web/20140719063725/http://www.altdev.co/2011/08/08/interesting-vertex-shader-trick/

       1  
    ( 0, 2)
    [-1, 3]   [ 3, 3]
        .
        |`.
        |  `.  
        |    `.
        '------`
       0         2
    ( 0, 0)   ( 2, 0)
    [-1,-1]   [ 3,-1]

    ID=0 -> Pos=[-1,-1], <PERSON>=(0,0)
    ID=1 -> Pos=[-1, 3], <PERSON>=(0,2)
    ID=2 -> Pos=[ 3,-1], <PERSON>=(2,0)
    */

    texcoord.x = (vertID == 1) ?  2.0 :  0.0;
    texcoord.y = (vertID == 2) ?  2.0 :  0.0;

	return texcoord * vec2(2.0, -2.0) + vec2(-1.0, 1.0);
}


vec2 flipTexCoord(in vec2 tc) {
    return tc * vec2(1.0, -1.0) + vec2(0.0, 1.0);
}


vec4 flipTexCoord(in vec4 tc) {
    return tc * vec4(1.0, -1.0, 1.0, -1.0) + vec4(0.0, 1.0, 0.0, 1.0);
}


#define USE_UINT_COLOR 0
#define OIT_SORT_FRAG_ON_DRAW 0

struct OITData {
#if USE_UINT_COLOR
	uint color;
#else
    vec4 color;
#endif
	float depth;
	uint prev;
};
