#version 460
#extension GL_GOOGLE_include_directive : enable

#include "FFCodeHeader.glsl"

layout (binding = 2)  uniform sampler2D samplerColormap;


layout (location = 0) in vec3 inNormal;
layout (location = 1) in vec2 inUV;
layout (location = 2) in vec3 inColor;
layout (location = 3) in vec3 inPos;

layout (location = 0) out vec4 outPosition;
layout (location = 1) out vec4 outNormal;
layout (location = 2) out vec4 outAlbedo;



float linearDepth(float depth)
{
	float z = depth * 2.0f - 1.0f; 
	//return (2.0f * fv * fv1) / (fv1 + fv - z * (fv1 - fv));	
	return (2.0f * 25 * 50000) / (50000 + 25 - z * (50000 - 25));	
}

void main() 
{
	float de=linearDepth(gl_FragCoord.z);
	outPosition = vec4(inPos, de);
	outNormal = vec4(normalize(inNormal) * 0.5 + 0.5, 1.0);
	outAlbedo =  texture(samplerColormap, inUV) * vec4(inColor, 1.0);
}