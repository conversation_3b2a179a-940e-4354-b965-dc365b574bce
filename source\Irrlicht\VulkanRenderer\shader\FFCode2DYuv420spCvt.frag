#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;


layout (binding = 2)  uniform sampler2D g_tex0_sampler;
layout (binding = 3)  uniform sampler2D g_tex1_sampler;

layout(location = 0) in vec4 i_colorD;
layout(location = 1) in vec2 i_tex0;

#define PI 3.1415926
//========================================= PS ========================================

//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;

// adding pixel shader
void main()
{

	float Y=texture(g_tex0_sampler,i_tex0).r;
	vec2 uv=texture(g_tex1_sampler,i_tex0).rg;
	
	vec4 YUVA=vec4(Y,
	uv.r- 0.5		,
	uv.g- 0.5		,
	1.0  );
#if RGBA_ORDER
	vec4 color = vec4(
		YUVA.x+		YUVA.y* 1.765		,
		YUVA.x+		YUVA.y* -0.343	+	YUVA.z * -0.711,
		YUVA.x+                       YUVA.z*1.4,	
		1.0)  ; 
#else
	vec4 color = vec4(
		YUVA.x+                       YUVA.z*1.4,
		YUVA.x+		YUVA.y* -0.343	+	YUVA.z * -0.711,
		YUVA.x+		YUVA.y* 1.765						,
		1.0)  ; 
#endif
//color.a=(color.r+color.g+color.b);
outFragColor = color;//*vec4(vec3(cos( (fract(i_tex0.x*240)-0.5) *PI*2)*cos((fract(i_tex0.y*135)-0.5)*PI*2)*0.15+0.85),1);

}

