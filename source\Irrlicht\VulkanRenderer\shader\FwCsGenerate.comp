#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
#include "GsParticleShared.h"

// Increase workgroup size for better parallelism
layout (local_size_x = 64) in;
#define IS_CS 1
#include "FwParticleShaderShared.glsl"

// Original function kept for reference
void GenParticle(VSParticle pt)
{
    if (freeCount < CS_LOCAL_SIZE_X*10)
        return;

    int freeIdx = int(atomicAdd(freeCount, -1))-1;
    uint newShowIdx = atomicAdd(showCountOut, 1);

    if (freeIdx < CS_LOCAL_SIZE_X || newShowIdx >= MaxPtcCount)
    {
        atomicAdd(freeCount, 1);
        atomicAdd(showCountOut, -1);
        memoryBarrier();
        return;
    }
    uint ffIdx = atomicAdd(freeB, 1);
    uint pid = FreeIds[ffIdx % MaxPtcCount];

    Pts[pid] = pt;
    
    idsOut[newShowIdx] = pid;
    memoryBarrier();
}

void main() 
{
    // Use workgroup parallelism with shared memory
    // Each invocation will handle a chunk of work
    uint globalIdx = gl_GlobalInvocationID.x;
    uint localSize = gl_WorkGroupSize.x;
    
    // Pre-compute available particles to generate
    uint availableParticles = min(GenCount, MaxPtcCount - showCountOut);
    uint particlesPerThread = (availableParticles + localSize - 1) / localSize;
    uint startIdx = globalIdx * particlesPerThread;
    uint endIdx = min(startIdx + particlesPerThread, availableParticles);
    
    // Only execute if there's work to be done and enough free particles
    if (startIdx < availableParticles && freeCount > CS_LOCAL_SIZE_X * 10) {
        // Local counters to minimize atomic operations
        uint localGeneratedCount = 0;
        
        // Process batch of particles
        for (uint i = startIdx; i < endIdx && freeCount > CS_LOCAL_SIZE_X; i++) {
            // Reserve free index and show index
            uint ffIdx = atomicAdd(freeB, 1) % MaxPtcCount;
            uint newShowIdx = atomicAdd(showCountOut, 1);
            atomicAdd(freeCount, -1);
            
            // Get particle ID from free list
            uint pid = FreeIds[ffIdx];
            
            // Generate particle
            Pts[pid] = PtsGen[i]; 
            
            // Add to output list
            idsOut[newShowIdx] = pid;
            
            localGeneratedCount++;
        }
    }
    
    // Wait for all threads to finish processing
    memoryBarrierShared();
    barrier();
    
    // Let thread 0 handle the final commands update
    if (globalIdx == 0) {
        drawIdxCmd = GlslVkDrawIndexedIndirectCommand(showCountOut*6*drawIdxCmdKld, 1, 0, 0, 0);
        drawNoIdxCmd = GlslVkDrawIndirectCommand(showCountOut, 1, 0, 0); // POINT LIST
        dsipCmd = GlslVkDispatchIndirectCommand(uint(ceil(float(showCountOut)/float(CS_LOCAL_SIZE_X))), 1, 1);
        
        // Update counters with single atomic operations
        atomicExchange(showCount, showCountOut);
        atomicExchange(showCountOut, 0);
        totalCount = freeCount + showCount;
        freeB = freeB % MaxPtcCount;
        freeF = freeF % MaxPtcCount;
    }
    
    memoryBarrier();
}