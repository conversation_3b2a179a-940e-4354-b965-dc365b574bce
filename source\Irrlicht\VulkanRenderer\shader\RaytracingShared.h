// RaytracingShared.h - Common definitions for raytracing shaders
// This file is included by both C++ code and GLSL shaders

#ifndef RAYTRACING_SHARED_H
#define RAYTRACING_SHARED_H

#ifdef __cplusplus
// C++ definitions
namespace irr {
namespace video {
#endif

// Raytracing descriptor set bindings
#define RT_BINDING_OUTPUT_IMAGE     0
#define RT_BINDING_ACCELERATION_STRUCTURE 1
#define RT_BINDING_CAMERA_UBO      2
#define RT_BINDING_SKY_UBO         3
#define RT_BINDING_LIGHT_UBO       4

// Geometry descriptor set bindings (set 1)
#define RT_BINDING_VERTEX_BUFFER   0
#define RT_BINDING_INDEX_BUFFER    1
#define RT_BINDING_NORMAL_BUFFER   2
#define RT_BINDING_MATERIAL_BUFFER 3

// Ray payload locations
#define RT_PAYLOAD_PRIMARY         0
#define RT_PAYLOAD_SHADOW          1

// Shader binding table offsets
#define RT_SBT_RAYGEN_GROUP        0
#define RT_SBT_MISS_GROUP          0
#define RT_SBT_SHADOW_MISS_GROUP   1
#define RT_SBT_HIT_GROUP           0

#ifndef __cplusplus
// GLSL-only definitions

// Camera uniform buffer structure
struct CameraUBO {
    mat4 viewMatrix;
    mat4 projMatrix;
    mat4 viewInverse;
    mat4 projInverse;
    vec3 cameraPos;
    float time;
};

// Sky/environment uniform buffer structure
struct SkyUBO {
    vec3 skyColor;
    vec3 horizonColor;
    vec3 sunDirection;
    float sunIntensity;
};

// Light uniform buffer structure
struct LightUBO {
    vec3 lightDirection;
    vec3 lightColor;
    float lightIntensity;
    vec3 ambientColor;
};

// Material structure (matches MaterialBuffer in shaders)
struct Material {
    vec3 diffuseColor;
    float roughness;
};

#endif // __cplusplus

#ifdef __cplusplus
} // namespace video
} // namespace irr
#endif

#endif // RAYTRACING_SHARED_H 