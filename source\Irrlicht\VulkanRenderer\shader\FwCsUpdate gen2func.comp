#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable

#define USE_MFLAG_LAND 0
#define HAS_TEXTURE 0
#include "GsParticleShared.h"



layout (local_size_x = CS_LOCAL_SIZE_X) in;
#include "colorutil.glsl"

#define IS_CS 
#include "FwParticleShaderShared.glsl"



float rand01(float seed, in vec2 uv)//0 ~ 1
{
	float result = fract(sin(seed * dot(uv, vec2(12.9797f, 78.733f))) * 739757.5253f);
	return result;
}

float rand(float seed, in vec2 uv)//-1 ~ 1
{
	float result = fract(sin(seed * dot(uv, vec2(12.9898f, 78.733f))) * 739757.5253f);
	//seed += 1.0f;
	return result*2-1;
}
vec3 RandomDir(float fOffset, vec2 uv,float fDiv)
{
	//float tCoord = fOffset;//(g_fGlobalTime + fOffset) / fDiv + randFac;
	return vec3(rand(fOffset,uv*0.198),rand(fOffset+17.251,uv*0.231),rand(fOffset+231.27912,uv*0.137));
}
// returns a random float in range (0, 1). seed must be >0!

mat3 GetRotYMatFromVec(vec3 vel)
{
	mat3 mRV;
	vec3 vn = (length(vel) == 0.0f) ? vec3(0, 1, 0) : normalize(vel);

	float rad = acos(dot(vec3(0, 1, 0), vn));
	float c = cos(rad), s = sin(rad), ac = 1.f - c;
	mat3 mRotV;
	vec3 nc = (cross(vn,vec3(0, 1, 0)));
	float x = nc.x, y = nc.y, z = nc.z;
	mRV[0][0] = c + ac*x*x;		mRV[0][1] = ac*x*y + s*z;	mRV[0][2] = ac*x*z - s*y; //m[0][3] = 0;
	mRV[1][0] = ac*x*y - s*z;	mRV[1][1] = c + ac*y*y;		mRV[1][2] = ac*y*z + s*x; //m[1][3] = 0;
	mRV[2][0] = ac*x*z + s*y;	mRV[2][1] = ac*y*z - s*x;	mRV[2][2] = c + ac*z*z; //m[2][3] = 0;
																					//m[3][0] = 0; m[3][1] = 0; m[3][2] = 0;								 m[3][3] = 1;

	return mRV;
}

vec3 getVecRotY(vec3 vel)
{
	mat3 mRV;
	vec3 vn = (length(vel) == 0.0f) ? vec3(0, 1, 0) : normalize(vel);

	float rad = acos(dot(vec3(0, 1, 0), vn));
	float c = cos(rad), s = sin(rad), ac = 1.f - c;
	mat3 mRotV;
	vec3 nc = (cross(vn,vec3(0, 1, 0)));
																	//m[3][0] = 0; m[3][1] = 0; m[3][2] = 0;								 m[3][3] = 1;

	return nc;
}

void GenParticle(int bufId,VSParticle pt)
{
	if (bufId==0 && freeCount< CS_LOCAL_SIZE_X*16 || bufId==1 && freeCount< CS_LOCAL_SIZE_X*128)
		return;

	int freeIdx= int(atomicAdd(freeCount,-1))-1;
	uint newShowIdx=atomicAdd(showCountOut,1);

	if (freeIdx<CS_LOCAL_SIZE_X*16 || newShowIdx>=MaxPtcCount)
	{
		atomicAdd(freeCount,1);
		atomicAdd(showCountOut,-1);
		memoryBarrier();
		return;
	}
	uint ffIdx=atomicAdd(freeB,1);
	uint pid= FreeIds[ffIdx % MaxPtcCount];

	Pts[pid]= pt;
	
	idsOut[newShowIdx]=pid;
	memoryBarrier();
}


#define PTC Pts[pid]	//VSParticle ptc= Pts[pid];


void OutParticle(uint pid)
{

	uint newShowIdx = atomicAdd(showCountOut,1);
	if (newShowIdx<MaxPtcCount)
		idsOut[newShowIdx] = pid;
	else
		atomicAdd(showCountOut,-1);
	memoryBarrier();
	return ;
}
void FreeParticle(uint pid)
{
#if 0
	uint toFreeIdx= atomicAdd(toFreeCount,1);
	if (toFreeIdx<MaxPtcCount)
		FreeIds[MaxPtcCount+toFreeIdx] = pid;
	else
		atomicAdd(toFreeCount,-1);
	memoryBarrier();
#else
	uint freeIdx=atomicAdd(freeCount,1);

	if (freeIdx<MaxPtcCount)
	{
		uint ffIdx=atomicAdd(freeF,1);

		FreeIds[ffIdx%MaxPtcCount] = pid;
	}
	else
		atomicAdd(freeCount,-1);
	memoryBarrier();
#endif
}




#define PTCBACK PTC=ptc



void generateEmber(in uint pid,in uint eid, in VSParticle ptc)
{
	VSParticle opt=ptc;
		opt.eid = eid;
			mat3 mRV= mat3(1);
			vec3 curVec = ptc.vel;
			Ember emb = Emb[opt.eid];

			uint count = min(256, emb.emc);   //��һ�����ܶ࣬���1��������Ÿ�0�����
											//curVec=mul( curVec, (vec3x3)mWV );
											//curVecN = normalize(mul( curVec,(vec3x3)mRotZ));

			opt.ofs.z = ptc.ofs.z;
			// if (bool(Emb[ptc.eid].cflag & (CFLAG_ImgGather | MF_PtMovTo)) ) 
            //     opt.pos = ptc.tcl; //fix child position
			// else
				opt.pos = ptc.pos;//+ (ptc.vel*g_fElapsedTime);
			opt.reb = emb.reb;
			vec3 vRandom = normalize(RandomDir(float(count) + randFac +  ptc.ofs.x,vec2(g_fGlobalTime,randFac*17), 11.31f));
			vRandom = clamp(vRandom,-1,1);



			if (bool(emb.gmode & GMODE_RotOnY)) mRV = GetRotYMatFromVec(ptc.vel);
            float spc = emb.spc * (bool(emb.cflag & CFLAG_Vec0OnZ) ? opt.ofs.z : 1.f);
                


			for (uint i = 0; i<count; i++)
			{

				//opt.pos= ptc.pos+ptc.vel*g_fElapsedTime*i/count;//�����ų�
				opt.ofs.x = opt.ofs.x + vRandom.x;// fmod(opt.ofs.x + vRandom.x, 1.f);
				float iratio = float(i) / count;

				vec3 vRand=RandomDir(iratio +  randFac + opt.ofs.x + opt.ofs.y,vec2(g_fGlobalTime,randFac*17), 17.31f);	
				float vRandLength = length(vRand);
				vRand = (vRandLength == 0.0) ? vec3(0.0, 1.0, 0.0) : vRand / vRandLength;//normalize() need check length too!
				vRand = clamp(vRand,-1,1);
				
				{

					float ik = (i + ((PTC.reb - 1)*0.5)) / count;
					uint gm=emb.gmode;
					switch (gm & GMODE_DirMask)
					{
					default:
					{
						opt.vel = (curVec*emb.spr + vRand*(spc + i*randFac));
					}
					break;

					case 0x2: //rot y ring xz
					{
						float a = -PI + 2 * PI*(ik);
						opt.vel = (bool(gm & 0x8000) ? (vec3(sin(a), 0, cos(a))* mRV) : vec3(sin(a), 0, cos(a))) * spc + curVec*emb.spr;
					}
					break;
					case 0x3: //rot z ring xy
					{
						float a = -PI + 2 * PI*(ik);

						opt.vel = vec3(sin(a), cos(a), 0)* spc + curVec*emb.spr;
					}
					break;					
					case 0x8: //Fibonacci Sphere 
					{

						opt.vel = FibonacciSphere(count,i)* spc + curVec*emb.spr;
					}
					break;
					
					case 0x10: //ring xz 1
					{
						float a = -PI + 2 * PI*(ik);
						opt.vel = vec3(sin(a), 0, cos(a)) *(spc + length(curVec)*((gm&0x200)>>9)*emb.spr)+ ((gm&0x100)>>8)*curVec*emb.spr;
					}
					break;
					case 0x11: //ring xy 1
					{
						float a = 2 * PI*(ik);
						opt.vel = vec3(cos(a), sin(a), 0) *(spc + length(curVec)*((gm&0x200)>>9)*emb.spr)+ ((gm&0x100)>>8)*curVec*emb.spr;
					}
					break;
					case 0x12: //ring zy 1
					{
						float a = 2 * PI*(ik);
						opt.vel = vec3(0, sin(a), cos(a)) *(spc + length(curVec)*((gm&0x200)>>9)*emb.spr)+ ((gm&0x100)>>8)*curVec*emb.spr;
					}
					break;		
					case 0x15://rotate
						float aa = 2 * PI / count, ab = g_fGlobalTime * ((emb.gmode & 0xFF0000) >> 16);
						float a = aa*i+ab;// -PI + 2 * PI*(ik)+;
						opt.vel = (bool(gm & GMODE_RotOnY) ? (vec3(sin(a), 0, cos(a))* mRV) : vec3(sin(a), 0, cos(a))) * spc + curVec*emb.spr;
					break;			
					case 0x1a: //ring reb
					{
						float a = atan(ptc.vel.y, ptc.vel.x) + 2 * PI*(ik); ;
						opt.vel = vec3(cos(a), sin(a), 0) * (spc + length(curVec)*emb.spr)* pow(0.7, Emb[ptc.eid].reb - ptc.reb);
					}
					break;
					case 0x1d:
					{
						
						opt.vel =  getVecRotY(curVec)*(spc + length(curVec)*emb.spr);
					}
					break;
					case 0x20:
					{
						opt.vel = vec3(spc, 0, 0) + curVec*emb.spr;
					}
					break;
					case 0x21:
					{
						opt.vel = vec3(0, spc, 0) + curVec*emb.spr;
					}
					break;
					case 0x22:
					{
						opt.vel = vec3(0, 0, spc) + curVec*emb.spr;
					}
					break;

					case 0x30:
					{
						float a = -PI + 2 * PI*(ik)+g_fGlobalTime;
						opt.vel = vec3(sin(a), 0, cos(a)) * spc;
					}
					break;

					
					}




				}

				opt.timer = opt.life =  max(0.00001f, Emb[opt.eid].life + vRand.z*Emb[opt.eid].life1) ;		//life>0 
		
                
				if (bool(emb.cflag & CFLAG_CycStartCvt))
					opt.timer=0.0;
				if (bool(emb.gmode & GMODE_GenTonPrT))                  opt.timer *= PTC.timer / PTC.life;  //life>0   max(0.00001f...)
				
				#if 0
				float dt=-ptc.timer;
				if (dt>0.0)
				{
					opt.pos = opt.pos+ opt.vel*dt;
    				//opt.vel = opt.vel * (1.0 - fFrameDecelerate * Emb[opt.eid].decml) + g_vFrameGravity * Emb[opt.eid].m;
					opt.timer-=dt;
					if (opt.timer<0) opt.timer=0;
				}
				#endif
				GenParticle(0,opt);
			}

}


void main() 
{
	// Current SSBO index
	uint index = gl_GlobalInvocationID.x;
	if (index >= showCount) 
		return;	
	vec2 uv=vec2(g_fGlobalTime,randFac*17);


	//if (index==0)	{	}

	uint pid= ids[index];

	VSParticle ptc = PTC;
	//if (index==0)		f0=ptc.timer;

	bool bNotLastConv=false;

	uint id=ptc.eid;
	if (id >= FW_MAX_TYPE_COUNT)	{FreeParticle(pid);		return;}

	bool needCvt = true;
	vec3 oldPos=ptc.pos;
	ptc.pos = ptc.pos+ ptc.vel*g_fElapsedTime;//min(ptc.timer,g_fElapsedTime);
#if USE_MFLAG_LAND
	if (  bool(Emb[ptc.eid].mflag & MFLAG_LandOnYMin) && 
	ptc.pos.y <= landMin.y) {
		ptc.pos.y = landMin.y * 2 - ptc.pos.y;
		ptc.vel.y = -ptc.vel.y;
		//ptc.timer=0;
	}
#endif
    ptc.vel = ptc.vel * (1.f - fFrameDecelerate * Emb[ptc.eid].decml) + g_vFrameGravity * Emb[ptc.eid].m;
	
	bool isCycleEmb=bool(Emb[id].cflag & CFLAG_CycleCvt);
	if (isCycleEmb && (Emb[id].stage < cycleStage || ptc.bid<expireBidMax))
	{
		ptc.reb=0;ptc.timer=0;
	}

	uint mf=Emb[id].mf;
    if ((mf & MF_MASK_Mov) == 0)
    {
        ptc.timer -= g_fElapsedTime;
        if (ptc.timer > 0.f)
        {
            needCvt = false;
			PTCBACK;
			//if ( int(ptc.bid)<maxBid)
            OutParticle(pid);

        } 
    }
    else
    {

        vec3  vt;


		
		if (bool(mf & MF_PtMovAt))
		{
			vec4 fv=Emb[id].fv;
            vt = (ptc.tcl - ptc.pos);
			
            float vtLen = max(0.001f, length(vt));

			if (vtLen < fv.y)
            {
				if (dot(vt,ptc.vel)<0)
				{
					ptc.timer = 0.f;
					ptc.pos = ptc.tcl;
					ptc.vel = vec3(0.0);
				}
				else
				{
					if ((mf & MF_PtMovInTo)!=0)
					{
						if (length(ptc.vel)*g_fElapsedTime<fv.y)
						{
							ptc.pos = ptc.tcl;
							ptc.vel = vec3(0.0);
						}
						else
							ptc.vel*=pow(fv.z,10);
					}
					if ((mf & MF_PtMovInCov)!=0)
						ptc.timer = 0.f;
				}
            }
            else
            {
				vec3 v=vt / pow(vtLen,fv.w);
				// vec3 nv=normalize(v);
				// float lv=length(v);
				// v= min(10,lv)*nv;
                ptc.vel =(ptc.vel+  fv.x * v *g_fElapsedTime)*fv.z ;
            }

		}
        if (bool(mf & (MF_PtMovAtG)))
        {
			vec4 fv1=Emb[id].fv1;
            vt = (pointerPos - ptc.pos);

            float vtLen = max(0.0001f, length(vt));
			if (pointerDown)
			{
				ptc.vel +=   (vt*fv1.x + pointerVel*fv1.z	)/ pow(vtLen,fv1.w ) ;
			}
        }
		ptc.timer -= g_fElapsedTime;
		if (ptc.timer > 0.f)
		{
			needCvt = false;
			PTCBACK;
			//if ( int(ptc.bid)<maxBid)
			OutParticle(pid);
		} 

    }


	bool needFree=needCvt;
	if (needCvt)
	{

		if (ptc.reb>0)
		{
			if (!isCycleEmb)
			ptc.reb--;
		}
		bNotLastConv = (ptc.reb > 0);

		if (bNotLastConv)
		{
			if (rand01(randFac*7.89167+ptc.ofs.x+ptc.ofs.y*0.587,uv)<Emb[id].c2skr-length(ptc.vel)*Emb[id].c2spd  )
				needCvt=false;
			//vec3 vRand = normalize(RandomDir(randFac*1.89 + ptc.ofs.x*2.79,uv, 2.791f));

			ptc.timer = ptc.life = max(0.00001f, Emb[id].life + (rand01(randFac*1.89 + ptc.ofs.x*2.79,uv))*Emb[id].life1);
			PTCBACK;
			//if ( int(ptc.bid)<maxBid)
			OutParticle(pid);
			needFree=false;
		}
		


	}
	
	VSParticle opt = ptc;


	if (needCvt) 
	{
		//opt = ptc;

		uint genEid = ptc.eid + (bNotLastConv ? Emb[id].tt2 : Emb[id].tt);
		if ( (Emb[genEid].gmode & GMODE_GenOfs)!=0  ){
				genEid += (ptc.flag&PFLAG_ConvOfs_MASK);
				//opt.flag = opt.flag & (~PFLAG_ConvOfs_MASK);
		}
		if (genEid != ptc.eid)
		{
			if (Emb[genEid].gmode == GMODE_ExtDataCv)
			{
				if (Emb[genEid].tt!=0) generateEmber(pid,genEid+Emb[genEid].tt,ptc);
				if (Emb[genEid].tt1!=0) generateEmber(pid,genEid+Emb[genEid].tt1,ptc);	
				if (Emb[genEid].tt2!=0) generateEmber(pid,genEid+Emb[genEid].tt2,ptc);
			}
			else
			generateEmber(pid,genEid,ptc);
		}
	}


	{

		int cv1=Emb[id].tt1 ;
		if (cv1 != 0 && (ptc.flag & PFLAG_FORBID_CV1)==0)
		{
			opt.eid = ptc.eid + cv1;
#if 0
			if (Emb[opt.eid].gmode == GMODE_ExtDataCv)
			{
				if (Emb[opt.eid].tt!=0) generateEmber2(pid,Emb[opt.eid].tt,ptc,opt);
				if (Emb[opt.eid].tt1!=0) generateEmber2(pid,Emb[opt.eid].tt1,ptc,opt);	
				if (Emb[opt.eid].tt2!=0) generateEmber2(pid,Emb[opt.eid].tt2,ptc,opt);
				
			}
			else
			generateEmber2(pid,0,ptc,opt);


#else
			if ( (Emb[opt.eid].gmode & GMODE_GenOfs)!=0  ) {
				opt.eid = opt.eid+ (opt.flag&PFLAG_ConvOfs_MASK);
				//opt.flag = opt.flag & (~PFLAG_ConvOfs_MASK);
			}
			Ember emb = Emb[opt.eid];
			mat3 mRV;
			if ((emb.gmode & GMODE_RotOnY)!=0) mRV = GetRotYMatFromVec(PTC.vel);

			//opt.ofs = ptc.ofs;
            if ((emb.gmode & GMODE_GenZonPR)!=0)
				opt.ofs.z = CalcRatio(PTC.timer / PTC.life, Emb[ptc.eid].mpr);

			uint count = min(6, emb.emc);
            float spc = emb.spc * (bool(emb.cflag & CFLAG_Vec0OnZ) ? opt.ofs.z : 1.f);

			opt.reb = emb.reb;

			//opt.col = vec4(DW2B_R(Emb[opt.eid].bcol), DW2B_G(Emb[opt.eid].bcol), DW2B_B(Emb[opt.eid].bcol), DW2B_A(Emb[opt.eid].bcol)) / 255.0f;

			vec3 baseVel = ptc.vel*emb.spr;


            switch (emb.gmode & 0xFF)
            {
				// case 1: GenParticle(1,opt);break;
				// case 0x3: GenParticle(1,opt);break;
				// case 0x4: GenParticle(1,opt);break;
				// case 0x5: GenParticle(1,opt);break;
				// case 0x6: GenParticle(1,opt);break;
				// case 0x7: GenParticle(1,opt);break;
				// case 0x8: GenParticle(1,opt);break;
				// case 0x9: GenParticle(1,opt);break;
				// case 0xa: GenParticle(1,opt);break;
				// case 0xb: GenParticle(1,opt);break;
				// case 0xc: GenParticle(1,opt);break;
				// case 0xd: GenParticle(1,opt);break;
				// case 0xe: GenParticle(1,opt);break;
				// case 0x25: GenParticle(1,opt);break;
				// case 0x26: GenParticle(1,opt);break;
				// case 0x27: GenParticle(1,opt);break;
				// case 0x30: GenParticle(1,opt);break;
				// case 0x28: GenParticle(1,opt);break;
				// case 0x29: GenParticle(1,opt);break;
				// case 0x31: GenParticle(1,opt);break;
				// case 0x32: GenParticle(1,opt);break;
				// case 0x33: GenParticle(1,opt);break;
				// case 0x34: GenParticle(1,opt);break;
				// case 0x35: GenParticle(1,opt);break;
				// case 0x36: GenParticle(1,opt);break;
				// case 0x37: GenParticle(1,opt);break;
				// case 0x38: GenParticle(1,opt);break;
				// case 0x39: GenParticle(1,opt);break;

                default:
				{

                    float epc = g_fElapsedTime / count;
                    for (uint i = 1; i <= count; i++)
                    {
                        float ir = float(i) / float(count);
                        opt.pos = oldPos + ir * (ptc.pos- oldPos); //

                        vec3 vRand = (RandomDir(ir*0.30867 + ir*randFac + ptc.ofs.x+ ptc.timer*0.859 + opt.pos.x, uv,117.31f));
                        opt.vel = (baseVel + normalize(vRand) * spc);
                        opt.timer = opt.life = max(0.00001f, emb.life + vRand.z * emb.life1);
                        if (bool(emb.gmode & GMODE_GenTonPrT))
                            opt.timer *= PTC.timer / PTC.life;
                        opt.ofs.x += vRand.x; // fmod(opt.ofs.x + vRandom.x, 1.f);
                        GenParticle(1,opt);
                    }
                }
                break;
				
                case 0x2: //
				case 0x15:
				{
				//opt.timer = emb.life;

                    float aa = 2 * PI / count, ab = g_fGlobalTime * ((emb.gmode & 0xFF0000) >> 16);
                    float vc = ((emb.gmode & 0xFF000000) >> 24) / 100.f;

                    float fRand = rand(randFac*1.89 + ptc.ofs.x*2.79,uv);
                    opt.timer = opt.life = max(0.00001f, emb.life + fRand * emb.life1);
                    if (bool(emb.gmode & GMODE_GenTonPrT))
                        opt.timer *= PTC.timer / PTC.life;
                    for (uint i = 0; i < count; i++)
                    {
                        float a = aa * i + ab;
                        opt.vel = (bool(emb.gmode & GMODE_RotOnY) ? (vec3(sin(a), 0, cos(a))* mRV) : vec3(sin(a), 0, cos(a))) * spc + baseVel;
                        opt.pos = ptc.pos + opt.vel * vc;
                        GenParticle(1,opt);
                    }
                }
                break;

            }

#endif
		}

	}

	//ptc.timer= ptc.timer- g_fElapsedTime;
	//ptc.pos += ptc.vel * g_fElapsedTime;
	//if (index==0)		{f1=ptc.timer;f2=g_fElapsedTime;}

	if (needFree) FreeParticle(pid);
}
