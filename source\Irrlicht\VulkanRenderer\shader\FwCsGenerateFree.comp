#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
#include "GsParticleShared.h"



layout (local_size_x = 16) in;
//#include "colorutil.glsl"

#include "FwParticleShaderShared.glsl"


layout(binding = 1) readonly uniform  cbOnlyOnce //once
{
	Ember Emb[FW_MAX_TYPE_COUNT];
};
layout(std430, binding = 2) buffer  cbPtc 
{   
	 VSParticle Pts[]; 
};
layout(std430, binding = 3) readonly buffer  cbPtcGen
{   
	 VSParticle PtsGen[]; 
};
layout (std430,binding = 5)  buffer CountBuffer 
{
	uint showCount,showCountOut,freeCount,toFreeCount;
	float f0,f1,f2,f3;
};
layout (std430,binding = 6)  readonly buffer IdsBuf
{
	uint ids[];
};
layout (std430,binding = 7)  buffer IdsOutBuf
{
	uint idsOut[];
};
layout (std430,binding = 8)  buffer FreeIdsBuf
{
	uint FreeIds[];
};


void GenParticle(VSParticle ptc)
{
	if (freeCount<CS_LOCAL_SIZE_X)
		return;
	int freeIdx= int(atomicAdd(freeCount,-1))-1;
	uint newShowIdx=atomicAdd(showCountOut,1);

	if (freeIdx<CS_LOCAL_SIZE_X || newShowIdx>=MaxPtcCount)
	{
		atomicAdd(freeCount,1);
		atomicAdd(showCountOut,-1);
		return;
	}

	uint pid= FreeIds[freeIdx];

	Pts[pid]= ptc;
	
	idsOut[newShowIdx]=pid;
	memoryBarrier();
}

void DoFreeParticleIdx(uint i)
{

	uint freeIdx=atomicAdd(freeCount,2);
	if (freeIdx<MaxPtcCount)
		FreeIds[freeIdx] = FreeIds[MaxPtcCount+i];
	else		atomicAdd(freeCount,-1);
	memoryBarrier();
}


void main() 
{
	// Current SSBO index
	uint i = gl_GlobalInvocationID.x;
#if 1
	//for (int i=0;i<GenCount;i++) 
	if (i<GenCount && i<toFreeCount)
	{

		uint newShowIdx=atomicAdd(showCountOut,1);

		if (newShowIdx>=MaxPtcCount)
		{
			atomicAdd(showCountOut,-1);
			DoFreeParticleIdx(i);
			return;
		}



		uint pid= FreeIds[MaxPtcCount+i];

		Pts[pid]= PtsGen[i];
		
		idsOut[newShowIdx]=pid;
		memoryBarrier();
		
	}
	else
	{
		if (GenCount>toFreeCount && i<GenCount)
		{

			GenParticle(PtsGen[i]);
		}
		else if (GenCount<toFreeCount && i<toFreeCount)
		{
			DoFreeParticleIdx(i);
		}
	}
#else


	    if (i<toFreeCount)
		{
			DoFreeParticleIdx(i);
		}

		barrier();
		if ( i<GenCount)
		{

			GenParticle(PtsGen[i]);
		}
#endif
	//maxCount=MaxPtcCount;
}