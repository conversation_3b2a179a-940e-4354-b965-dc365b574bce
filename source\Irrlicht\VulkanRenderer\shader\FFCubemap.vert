#version 460
#extension GL_GOOGLE_include_directive : enable
#define USE_SPECULAR 0


// References HLSL
// Flow control: http://msdn.microsoft.com/en-us/library/bb509600(VS.85).aspx
// Semantics: http://msdn.microsoft.com/en-us/library/bb509647(VS.85).aspx

#include "FFCodeHeader.glsl"

	layout(location = 0) in vec3 i_pos;
	layout(location = 1) in vec3 i_norm;
	layout(location = 2) in vec4 i_color;
	layout(location = 3) in vec2 i_tex0;

layout (location = 0) out vec3 outPos;
layout (location = 1) out vec3 outNormal;
layout (location = 2) out vec3 outViewVec;
layout (location = 3) out vec3 outLightVec;

// adding vertex shader code
void main()
{	

vec4 worldPos = vec4(i_pos,1) * g_mWorld ;
	vec4 cameraPos = worldPos * g_mView ; //Save cameraPos for fog calculations

	gl_Position =  cameraPos * g_mProj;

	outPos = worldPos.xyz;
	outNormal = normalize(  i_norm * mat3(g_mWorld)  );

	vec3 lightPos = g_lights[0].Position.xyz;
	outLightVec = lightPos.xyz - outPos.xyz;
	outViewVec =  g_eyePos  -outPos.xyz;

}

