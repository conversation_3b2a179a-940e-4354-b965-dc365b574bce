#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;


layout (binding = 2)  uniform sampler2D g_tex0_sampler;
layout (binding = 3)  uniform sampler2D g_tex1_sampler;



	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;




//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;

// adding pixel shader
void main()
{
#if 1
	vec2 dudv= texture(g_tex1_sampler,i_tex0).rg;
	outFragColor =  texture(g_tex0_sampler,i_tex0+dudv) * i_colorD; 
 
#else
outFragColor = vec4(texture(g_tex0_sampler,i_tex0).rgb, i_colorD.a); 
#endif
}