#version 460
#extension GL_GOOGLE_include_directive : enable

#define USE_SPECULAR 1

// References HLSL
// Flow control: http://msdn.microsoft.com/en-us/library/bb509600(VS.85).aspx
// Semantics: http://msdn.microsoft.com/en-us/library/bb509647(VS.85).aspx

#include "FFCodeHeader.glsl"
#include "../DefineShareWithShader.h"

	layout(location = 0) in vec3 i_pos;
	layout(location = 1) in vec3 i_norm;
	layout(location = 2) in vec4 i_color;
	layout(location = 3) in vec2 i_tex0;





	layout(location = 0) out vec2 o_tex0;

	layout(location = 2) out vec4 o_colorD;
#if USE_SPECULAR 
	layout(location = 1) out vec3 o_wNorm;       //world space normal
	layout(location = 3) out vec4 o_colorS;

#endif
	layout(location = 4) out vec4 o_wPos;       //world space 

// adding vertex shader code
void main()
{	vec4 worldPos = vec4(i_pos,1) * g_mWorld ;
	o_wPos = worldPos;
	vec4 cameraPos = worldPos * g_mView ; //Save cameraPos for fog calculations


	vec3 worldNormal = normalize(  i_norm * mat3(g_mWorld)  );
	o_wNorm = worldNormal;
	ColorsOutput cOut = CalcLighting2( worldNormal, worldPos.xyz, cameraPos.xyz, i_color );  //FL 9.1 only support RGBA in VS 
	o_colorD = vec4(cOut.Diffuse.rgb , i_color.a*g_material.Diffuse.a);
	//output.colorD= vec4(g_iLightCount*2.0f,0,0,1.f);
#if USE_SPECULAR 
	o_colorS = cOut.Specular ;
	//o_colorS.a+=g_material.Emissive.a;
#endif
 
	o_tex0 = i_tex0 ;

	gl_Position =  cameraPos * g_mProj;
	//vec4 diffuseSwizzle = input.color; // swizzle the input rgba channels into D3D11 order

}


#if 0







// adding vertex shader code
VS_OUTPUT_2COORDS Coords2TVS( VS_INPUT_2COORDS input )
{
	VS_OUTPUT_2COORDS output = (VS_OUTPUT_2COORDS)0;

	//output our final position in clipspace
	vec4 worldPos = mul( vec4(input.pos,1), g_mWorld );
	vec4 cameraPos = mul( worldPos, g_mView ); //Save cameraPos for fog calculations
	output.pos = mul( cameraPos, g_mProj );

	output.wPos = worldPos.xyz;

	vec4 diffuseSwizzle = input.color; // swizzle the input rgba channels into D3D10 order

		vec3 worldNormal = normalize( mul( input.norm, (vec3x3)g_mWorld ) );
		output.wNorm = worldNormal;
		ColorsOutput cOut = CalcLighting( worldNormal, worldPos.xyz, cameraPos.xyz, input.color.bgra );
		output.colorD = vec4(cOut.Diffuse.rgb,input.color.a);
#if USE_SPECULAR 
		output.colorS = vec4(cOut.Specular.xyz,0);
#endif
	output.tex0 = input.tex0;
	output.tex1 = input.tex1;
	return output;
}


//========================================= PS ========================================
// adding textures and samplers
[[vk::binding(2,0)]]  Texture2D g_tex0 : register(t0);
//Texture2D g_tex1 : register(t1);
[[vk::binding(2,0)]] SamplerState g_tex0_sampler : register(s0);
//SamplerState g_tex1_sampler : register(s1);



// adding pixel shader
vec4 StandardPS( VS_OUTPUT input ) : SV_Target
{
	//return vec4(1,1,0,1);
	vec4 normalColor;// = vec4(0, 0, 0,0);
	vec4 tex1 = g_tex0.Sample( g_tex0_sampler, input.tex0 );
	normalColor = (tex1*input.colorD ) 
#if USE_SPECULAR 	
		+ input.colorS
#endif
		;

	return saturate(normalColor);
}
#endif