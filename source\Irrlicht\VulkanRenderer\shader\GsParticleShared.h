#ifndef FW_PARTICLE_SHARED_HEADER_FILE
#define FW_PARTICLE_SHARED_HEADER_FILE
#define FW_HIGH_PERFORMANCE 0
#define FW_MAX_TYPE_COUNT 2000  //USE storage buffer now, no limit //max 4096 vectors(32bit*4) : 4096*4*4/(80) =819   /96=682   /128=512
#define FW_USE_TEXARRAY     1
#define FW_USE_SCALESW      0
#define FW_FOR_HDR10        1   // for HDR video output
#define FW_MAX_PtCloudPoints  1024//point cloud count
#define FW_MAX_PtClouds			64//point cloud count
#define FW_GEN_MAX 51200u
#define FW_MAX_POINTER 8
#define FW_MAX_PtrLight	8


#define FW_VTX_MATS		1

#define FW_LIMIT_BOX    0

//#define FW_MAX_TYPE_COUNT 8   //max 4096 vectors(32bit*4) : 4096*4*4/(80) =819   /96=682   /128=512
//#define FW_GEN_MAX 8
#define PT_BANDS 32
#define HAS_BAND_V 0
#define FW_HAS_IMG_COLOR  1 
#define PRE_BG_DRAW_TO_FWIMG  0	


#define KLD_MIRROR  1
#define FW_USE_SHADOWMAP    1
#define FW_TO_MIDI      1
#define FW_HAS_STAGE 0


#define GMODE_GenZonPR		0x001000
#define GMODE_GenTonPrT		0x002000		// gen t on parent t
#define GMODE_ImgCol	    0x004000		// gen t on parent t
#define GMODE_RotOnY		0x008000		// rotate on vec Y direction
#define GMODE_LifeOnPv0     0x010000      //
#define GMODE_LifeOnPv1     0x020000
#define GMODE_Retarget      0x100000      //set tcl on parent pos
#define GMODE_VelOnEqvPeak  0x200000
#define GMODE_VelRotateMask 0x0F000000   

#define GMODE_ExtDataCv     0xF0         //data to gen up to 3 chilldren
#define GMODE_DirMask       0x3F      // direction 
#define GMODE_TYPE_CLOUD    0x1f
#define GMODE_WITH_SPR      0x100   //for gm  0x10 0x11 0x12 
#define GMODE_WITH_SPR2     0x200   //  spc += curVec*fv1.x*fv1.x
 
//#define GMODE_Params        0x0xFFFF0000
// 
//particle flag
//PFLAG only for img(like text img) fw, see pfwSetImage!!!!!!!!!!!!!!!!!
#define PFLAG_GenOfsMask    0x00000FF0         //gen Offset on 
#define PFLAG_GenOfsOL		0x00000010        //outline ofs
#define PFLAG_GenOfs        0x00000100		// 1 of 9  center=0 , out=+1 ofs
#define PFLAG_GenOfsRatio   0x00000200        //small ofs ratio
#define PFLAG_GenOfsNull	0x00000400        //f
//#define PFLAG_UpdateStop	0x10000000        //f

//#define PFLAG_ConvOfs_MASK  0x0000000Fu  // convert offset mask
#define PFLAG_FORBID_CV1    0x00004000u
#define PFLAG_NO_KLD		0x00010000u


#define CFLAG_TexRtoA		0x00000001u 
#define CFLAG_SpcOnZ		0x00010000u //ofs z
#define CFLAG_RMul          0x00020000u	//no scale
#define CFLAG_ChgHSLAMM		0x00000002u         // COL 2
#define CFLAG_ChgCol		0x00000004u         // COL 1
#define CFLAG_ChgColOnRatio	0x00000008u 
#define CFLAG_ChgCol_OnlyA	0x00000010u         // COL 1

#define CFLAG_ImgTex		0x00200000u	// size on r
#define CFLAG_VelonRatio	0x00400000u	// v on z

#define CFLAG_PtrLight		0x01000000u	// point light
#define CFLAG_UpdateStopCV1	0x08000000u   //SST  cv1 ON ,c1rat should <= 0.1

#define CFLAG_CycleCvt		0x10000000u	// wait FCF_EmbCycleStep command  STAGE
#define CFLAG_CycStartCvt	0x20000000u	// Cycle Start when convert to it
#define CFLAG_UpdateDecA	0x40000000u   //SST dec ptc alpha at last, then no convert, unless convert to mid:0 by setVtxMat v1type 1
#define CFLAG_UpdateStop	0x80000000u   //SST      

//#define TFLAG_ColMask		0x000000FF	// 
#define TFLAG_ColMaskInv	0x00000100u
#define TFLAG_ColMaskRandom	0x00000200u
#define TFLAG_ColMaskIds	0x000000FFu
#define TFLAG_ColHueMask	0x0000000Fu	
#define TFLAG_ColHue		0x00000001u	
#define TFLAG_ColHueSin		0x00000002u	
#define TFLAG_ColSat		0x00000003u	
#define TFLAG_ColSatSin		0x00000004u	
#define TFLAG_ColLight		0x00000005u
#define TFLAG_ColLightSin	0x00000006u
#define TFLAG_ColHSL		0x00000007u
#define TFLAG_ColHSLSin	    0x00000008u
#define TFLAG_ColToN		0x00000010u	// morphcolor to next
#define TFLAG_AlphaOnSpd	0x00000020u	//  on speed

//#define TFLAG_OnBandv			// color on band v
#define TFLAG_OnBandV_Mask	0xFFFF0000u
#define TFLAG_OnBandV_A		0xF0000000u
#define TFLAG_OnBandV_H		0x0F000000u
#define TFLAG_OnBandV_S		0x00F00000u
#define TFLAG_OnBandV_L		0x000F0000u



#define LANDF_LandOnXMin	0x00000001u
#define LANDF_LandOnXMax	0x00000002u
#define LANDF_LandOnYMin	0x00000010u
#define LANDF_LandOnYMax	0x00000020u
#define LANDF_LandOnZMin	0x00000100u
#define LANDF_LandOnZMax	0x00000200u


//GF only for CPU, 1st gen
#define GF_SetLifeTime 	    0x00000001u

#define MF_PtMovAt 	        0x00000001u	//just towards 
#define MF_PtMovAtG		    0x00000002u	//Gravity on distance
#define MF_PtMovRand		0x00000004u	//Gravity on distance
#define MF_PtMovAtPath		0x00000008u	//Gravity on distance

#define MF_MASK_Mov         0x0000000Fu
#define MF_PtMovInTo		0x00000010u	//in range, force to tgt
#define MF_PtMovInCov		0x00000020u	//in range, convert 
#define MF_PtMovInNoSpdChg  0x00000040u //in range, do not change speed

#define MF_PtMovAtPIdMask	0x0000FF00u	//mov tgt id 
#define MF_MASK_

#define MF_Dec_on_OrigRat   0x00010000u


//FRAME
//famce command
//#define FCF_EmbCycleStep    0x00000001u 

#define CS_LOCAL_SIZE_X 128

#define OIT_BufSize 1024*1024*32

#endif

/*
MF_PtMovAt
fv.x    acc spd
fv.y    min distance
fv.z    dec spd mul
fv.w    1/pow(vtLen,*)

MF_PtMovAtG
fv1.x   PT DOWN: 
*/
