#version 460
#extension GL_GOOGLE_include_directive : enable

precision highp float;

layout (binding = 0) uniform UBO 
{


	float len;
	float pad1;
	float pad2, pad3;

	int i00, i01, i02, i03;
	vec3 lightDir;
	float heightBase;

	vec4 pad[15 - 3];

	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w
} ubo;

layout (binding = 2)  uniform sampler2D g_tex0_sampler;




	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;




//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;


void main()
{

	float val = texture(g_tex0_sampler, i_tex0-ubo.len*ubo.lightDir.xy/ubo.res).r ; // current fragment's contribution

	outFragColor = vec4(1,1,1,val)*i_colorD;
}
