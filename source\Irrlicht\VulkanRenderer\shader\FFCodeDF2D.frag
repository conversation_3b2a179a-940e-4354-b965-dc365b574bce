// Distance field texture
#version 460
#extension GL_GOOGLE_include_directive : enable

precision highp float;
// References HLSL
// Flow control: http://msdn.microsoft.com/en-us/library/bb509600(VS.85).aspx
// Semantics: http://msdn.microsoft.com/en-us/library/bb509647(VS.85).aspx

// Constants
#define FIX_WAVE  0
#define PI  3.1415927
#define PI2 6.2831853
#define USE_SPECULAR 1
#include "FFCodeHeader.glsl"
#include "FwPtcDistanceFunctions.glsl"

layout (binding = 2)  uniform sampler2D g_tex0_sampler;

layout(location = 0) in vec2 i_tex0;
layout(location = 1) in vec3 i_wNorm;       //world space normal
layout(location = 2) in vec4 i_colorD;
layout(location = 3) in vec4 i_colorS;
layout(location = 4) in vec4 i_wPosVec4;
//========================================= PS ========================================
//SamplerState g_tex1_sampler : register(s1);
layout (location = 0) out vec4 outFragColor;
const float wavelength[8]={1.1,1.6,1.5,2.2,0.3,2,1,10};
const float speed[8]={1.1,1.6,1.5,2.2,0.3,2,1,0};

vec4 getColor(vec2 uv)
{
	vec4 c =  vec4(step(0.5,fract(uv*10)),0,1 );
	return c;
}

// /https://catlikecoding.com/unity/tutorials/flow/waves/
void GerstnerWave (	vec4 wave, vec3 p, inout vec3 tangent, inout vec3 binormal) {
	float steepness = wave.z;
	float wavelength = wave.w;
	float k = fv * PI / wavelength;
	float c = sqrt(0.68 / k);
	vec2 d = normalize(wave.xy);
	float f = k * (dot(d, p.xz) 
	-   c *gTime
	);
	float a = steepness / k;
	
	//p.x += d.x * (a * cos(f));
	//p.y = a * sin(f);
	//p.z += d.y * (a * cos(f));

	tangent += vec3(
		-d.x * d.x * (steepness * sin(f)),
		d.x * (steepness * cos(f)),
		-d.x * d.y * (steepness * sin(f))
	);
	binormal += vec3(
		-d.x * d.y * (steepness * sin(f)),
		d.y * (steepness * cos(f)),
		-d.y * d.y * (steepness * sin(f))
	);
	// return vec3(
	// 	d.x * (a * cos(f)),
	// 	a * sin(f),
	// 	d.y * (a * cos(f))
	// );
}
//const vec4 _Wave[]={			vec4(1,0,0.5,10),			vec4(0,1,0.25,20),			vec4(1,1,0.15,10),vec4(0,-1,		0.25,50),					vec4(0,0.23,		0.25,210),			vec4(-0.1,1.5,		0.25,100),			vec4(-0.2,0.1,		0.25,68),			vec4(0,1.8,			0.25,18),			vec4(0.11,2,		0.25,31),			vec4(0.22,0.6,0.25,11),			vec4(-0.10,1.3,0.25,18),};
	

/// WAVE SOURCE **********************
float fH_w(float d,float wh,float t){
	float x=d;
	//float t= (fract(gTime*0.2))*0.1;
	float c=(x-t)*1;
	//return wh*cos((c*fv2))*max(0,PI*3.5-c*c*16)*0.1;
	return wh*sin(c*fv1)*max(0,PI*(1-pow( exp(c*fv2)-1,2) ))*0.1;
}

float fH(vec2 tpos){
	float r= length(tpos);
	//return waveHeight(tpos.x*100,tpos.y*100)*0.1;
	  //cos(120.5*PI2*r-gTime*10)//*(1-r)//*0.5
	float sumy=0;
	for (int i=0;i<iv;i++) sumy+=fH_w(length(tpos-fvecs[i].xy),fvecs[i].z,fvecs[i].w);
//sumy = fH_w(length(tpos-fvecs[0].xy),fvecs[0].z*(1-fvecs[0].w),fvecs[0].w);
	return sumy;
	;
}

void main()
{	
	vec2 pos2= i_tex0.xy*2.0-vec2(1.0);
	vec3 i_wPos=i_wPosVec4.xyz;
	float r=length(pos2); 
#if FIX_WAVE
	vec3 tangent=vec3(0) ;
	vec3 binormal=vec3(0)  ;
	vec3 grpos= vec3(
		i_tex0.x,0,i_tex0.y
				//		pos2.x,0,pos2.y
		//-gTime//*0.005)*20000;
		*10.01
		)*80;		//1600
	//vec3 pp = grpos;
	for (int i = 0;i<8	;i++)
	GerstnerWave(fvecs[i], grpos, tangent, binormal);		
	vec3 bumpNormal = normalize(cross(binormal, tangent));
#else
	float h0=fH(pos2), hx=fH(pos2+vec2(0.001,0)), hy=fH(pos2+vec2(0,0.001));
	float dx=(hx-h0), dy=-(hy-h0);
	vec3 bumpNormal=  normalize(vec3(dx,0.32,dy));
#endif

		vec3 norm=(bumpNormal*mat3(g_mWorld));
#if USE_SPECULAR
		 //normalize( 	i_wNorm*mat3(g_mWorld)		);
		vec3 worldPos = i_wPos;
		vec3 toEye     =  vec3(0,0,-1);//normalize(g_eyePos - worldPos);
	ColorsOutput co;//=CalcLighting3(bumpNormal,vec3(pos2.x,0,pos2.y)*100,vec4(1,0,0,1));

  		vec3 lightDir = normalize(g_lights[0].Position.xyz -worldPos) ;

  		co.Diffuse.rgb = vec3(dot( lightDir, norm )  )  ;

		vec3 reflection =  //vec3(0,0,-1) + 2.f * bumpNormal  ;
		reflect(  -lightDir,norm );
  		co.Specular.rgb = vec3(max(0, pow( abs( dot(normalize(reflection), toEye) ),16)));
#endif
	if (passEnum==2)
	{
		vec3 nv=norm*mat3(g_mView)*mat3(g_mProj);
		outFragColor=//	vec4( (nv.xz)*0.01		,0		,(1-smoothstep(0,1,r)));//*;
		vec4( ((nv.xz))*0.16/sqrt(length(g_eyePos-i_wPos)),0,1);
	}
	else
	{
#if USE_SPECULAR
		float lightmul=dot( toEye, norm ) ;// *fv;
#if !FIX_WAVE
		vec3 dp=( worldPos);
		float wdis=min(1,500000.f/dot(dp,dp));
		outFragColor= //vec4(i_wPos,1);
			vec4(
			//	vec3(1)
		 	// max(vec3(0),co.Diffuse.rgb * i_colorD.rgb)// *lightmul*fv*( 2+texture(g_tex0_sampler,i_tex0).rgb*8)
			+ (co.Specular.rgb)
		//	+ getColor(i_tex0+vec2(bumpNormal.xz)).rgb
		//	+vec3(dx*10000,dy*10000,0)
		//	+(1+norm)/2
			//+vec3(h0*0.5)+0.5

			 ,		(
			 co.Specular.r*wdis
			//+bumpNormal.y*0.2+0.0
			 )
			//	,i_colorD.a
		//	,1
		//	,0
			);//*(1-smoothstep(0,0.1,r));//  *(1-smoothstep(0,0.75+0.25*fv3,r));
#else
		vec3 dp=( worldPos);
		//float wdis=min(1,5000.f/dot(dp,dp))*0.5;
		outFragColor=// vec4(0);
			vec4(
			//	vec3(1)
		 	// max(vec3(0),co.Diffuse.rgb * i_colorD.rgb)// *lightmul*fv*( 2+texture(g_tex0_sampler,i_tex0).rgb*8)
			+ (co.Specular.rgb)
			 ,		(
			 co.Specular.r*0
			//+bumpNormal.y*0.2+0.0
			 )
			//	,i_colorD.a
		//	,1
		//	,0
			);//*(1-smoothstep(0,0.01,r)) *(1-smoothstep(0,0.15+0.125*fv3,r));
#endif


#else
	outFragColor=vec4(0,0,0,0);
#endif
	}
	
	return;

}


