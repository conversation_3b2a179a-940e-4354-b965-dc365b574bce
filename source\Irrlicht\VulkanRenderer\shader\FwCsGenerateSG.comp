#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
//#extension GL_KHR_shader_subgroup_basic : enable //no need if other exist
#extension GL_KHR_shader_subgroup_ballot: enable
//#extension GL_KHR_shader_subgroup_arithmetic: enable
#include "GsParticleShared.h"

#define THREAD_SUBGROUPSIZE 32 // only nvida is 32?

layout (local_size_x = THREAD_SUBGROUPSIZE) in;
//#include "colorutil.glsl"
#define IS_CS 1
#include "FwParticleShaderShared.glsl"




void main() 
{
	if (gl_GlobalInvocationID.x>gl_SubgroupInvocationID)		return;
	int gc,sgc;
	//uint ccidx=gl_SubgroupInvocationID;
	if (subgroupElect()) {
		
		gc= int (min(GenCount, MaxPtcCount- showCountOut-CS_LOCAL_SIZE_X));
	}
	gc = subgroupBroadcastFirst(gc);
	sgc= gc/int(gl_SubgroupSize);
	//if (gl_SubgroupInvocationID==THREAD_SUBGROUPSIZE-1)		sgc+=gc % int(gl_SubgroupSize);
	uint baseId=sgc*gl_SubgroupInvocationID;
	for (int i=0;i<sgc;i++)
	{
		uint id=baseId + i;
 		uint pid= FreeIds[(freeB+id) % MaxPtcCount];
		Pts[pid]= PtsGen[id];
		idsOut[showCountOut+id]=pid;

	}
	//ccidx=subgroupMax(ccidx);
subgroupBarrier();
if (subgroupElect()
 //&& gl_GlobalInvocationID.x==0
 ) {
	#if 1
	int cgc=gc % int(gl_SubgroupSize);
	baseId=sgc * gl_SubgroupSize;
	for (int i=0;i<cgc;i++)
	{
		uint id=baseId + i;
 		uint pid= FreeIds[(freeB+id) % MaxPtcCount];
		Pts[pid]= PtsGen[id];		
		idsOut[showCountOut+id]=pid;
	}
	#endif
//subgroupBarrier();
	freeB+=(gc);
	showCountOut+= (gc);
	freeCount-= (gc);
    // could even do some complicated math on value
  
	drawIdxCmd = GlslVkDrawIndexedIndirectCommand(showCountOut*6*drawIdxCmdKld,1, 0,0,0);
	drawNoIdxCmd= GlslVkDrawIndirectCommand(showCountOut,1,0,0); // POINT LIST
	dsipCmd = GlslVkDispatchIndirectCommand( uint( ceil( float(showCountOut)/float(CS_LOCAL_SIZE_X))),1,1);
	//dsipCmd = GlslVkDispatchIndirectCommand( uint(ceil(1.0 )),1,1);
	//showCount = showCountOut;
	atomicExchange(showCount,showCountOut);
	atomicExchange(showCountOut,0);
	totalCount=freeCount+showCount;
	//mSubGroupSize=ccidx;
	freeB = freeB % MaxPtcCount;
	freeF = freeF % MaxPtcCount;
	memoryBarrier();//memoryBarrierBuffer();
}


}