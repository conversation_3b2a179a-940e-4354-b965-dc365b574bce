@echo off
rem THIS CODE AND INFORMATION IS PROVIDED "AS IS" WITHOUT WARRANTY OF
rem ANY KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO
rem THE IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
rem PARTICULAR PURPOSE.
rem
rem Copyright (c) Microsoft Corporation. All rights reserved.

setlocal

set fxDebug=1
set error=0

set FXCOPTS= -g -V

if %fxDebug% == 1 (
	set FXCOPTS=  -g -V 
)

set PCFXC=glslangValidator

:continue


call :CompileFX FwCsUpdate comp "-DUSE_MFLAG_LAND=1"
call :CompileFXN FwCsUpdate comp FwCsUpdateNL "-DUSE_MFLAG_LAND=0"
call :CompileFX FwCsGenerate comp 
call :CompileFX FwCsGenerateSG comp 

call :CompileFX FwPtcGLSL frag 
call :CompileFX FFCodeDF2D frag 

call :CompileFX FwCsExpand2v comp "-DHAS_KALEIDO=0"
call :CompileFXN FwCsExpand2v comp FwCsExpand2vKld "-DHAS_KALEIDO=1"
call :CompileFX FwCsDrawPass vert 
call :CompileFX FwCsDrawPoints vert 

call :CompileFX YuvProcess comp 

call :CompileFX FwPtcGLSL_oit frag 
call :CompileFX oit_resolve vert 
call :CompileFX oit_resolve frag 

rem glslangValidator -g -V -H -o Compiled\testOut.txt -S vert  FFCode.vert -e main   >> compiled\out1.txt

rem echo FwCsExpand2v_Point_comp_SpirV
rem glslangValidator  -g -V  -o Compiled\FwCsExpand2v_Point_comp_SpirV.h -S comp  FwCsExpand2v.comp -DOUTPTLIST=1 -e main  --vn SpirV_FwCsExpand2v_Point_comp || set error=1
echo.

if %error% == 0 (
    echo Shaders compiled OK ~~~~~~~~~~~~~~~~~~
) else (
    echo There were shader compilation errors!
)

endlocal
exit /b

:CompileFX
set fxc=%PCFXC% %FXCOPTS%   -o Compiled\%1_%2_SpirV_base.spv -S %2  %1.%2 -e main %3    --target-env vulkan1.1
echo.
echo %fxc%
%fxc% || set error=1
spirv-opt -O Compiled\%1_%2_SpirV_base.spv -o Compiled\%1_%2_SpirV.spv  
call spv2h.exe %1 %2 
exit /b

 

:CompileFXN
set fxc=%PCFXC% %FXCOPTS%  -o Compiled\%3_%2_SpirV_base.spv -S %2  %1.%2 -e main %4    --target-env vulkan1.1
echo.
echo %fxc%
%fxc% || set error=1
spirv-opt -O Compiled\%3_%2_SpirV_base.spv -o Compiled\%3_%2_SpirV.spv  
call spv2h.exe %3 %2 
exit /b



:CompileFX_O
set fxc=%PCFXC% %FXCOPTS%   -o Compiled\%1_%2_SpirV.h -S %2  %1.%2 -e main %3 --vn SpirV_%1_%2  --target-env vulkan1.1
set fxc1=%PCFXC% %FXCOPTS%  -o Compiled\%1_%2_SpirV.spv -S %2  %1.%2 -e main  
echo.
echo %fxc%
%fxc% || set error=1
exit /b

:CompileFXN_O
set fxc=%PCFXC% %FXCOPTS%   -o Compiled\%3_%2_SpirV.h -S %2  %1.%2 -e main %4 --vn SpirV_%3_%2   --target-env vulkan1.1
echo.
echo %fxc%
%fxc% || set error=1
exit /b

:needxdk
echo ERROR: CompileShaders xbox requires the Microsoft Xbox One XDK
echo        (try re-running from the XDK Command Prompt)