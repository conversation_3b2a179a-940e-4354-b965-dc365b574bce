#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;


layout (binding = 2)  uniform sampler2D g_tex0_sampler;




	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;


float LinearizeDepth(float depth)
{
  float n = 1.0; // camera z near
  float f = 2.0; // camera z far
  float z = depth;
  return (2.0 * n) / (f + n - z * (f - n));	
}

//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;

// adding pixel shader
void main()
{
#if 1
	outFragColor =  texture(g_tex0_sampler,i_tex0) * i_colorD; 
#elif 1
	float depth = texture(g_tex0_sampler, i_tex0).r;
	outFragColor = outFragColor = vec4(vec3(pow(1-depth,10)), 1.0);//vec4(vec3(1.0-LinearizeDepth(depth)), 1.0);
#else
outFragColor = vec4(texture(g_tex0_sampler,i_tex0).rgb, i_colorD.a); 
#endif
}