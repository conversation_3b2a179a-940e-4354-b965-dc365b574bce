#version 460
precision mediump int; precision highp float;
#extension GL_ARB_separate_shader_objects : enable
#extension GL_ARB_shading_language_420pack : enable
#extension GL_GOOGLE_include_directive : enable
#include "colorutil.glsl"
#define USE_LIGHT_TEX 0

//====================== SAME TO CPP ++++++++++++++++++++++++++++++
#define MMD_SABA_SCALE 100.f
#define MMDSD_NO_COLOR			0x00000001
#define MMDSD_IS_CAMSB			0x00000002
#define MMDSD_CTR_TRANSPARENT	0x00000010
//====================== SAME TO CPP ------------------------------

struct  MmdDynamicParam{
float   shadowRat,whiteRat,NdotLRat,NdotEyeRat;
int		shadowBlurCount,toonMulCount,colorMode,pad3;
};

layout (location = 0) in vec3 inPos;
layout (location = 1) in vec3 inNormal;
layout (location = 2) in vec3 inEyePos;
layout (location = 3) in vec2 inUV;
layout (location = 4) in vec3 inNorV;

layout (location = 6) in vec4 vtxpm;
layout (binding = 1) uniform UBO 
{
	mat4 	g_mWorld;
	mat4    g_mLightVP;
	vec4	Diffuse;
	vec4	Ambient;
	vec4	Specular;	
	vec4	lnv;
	ivec4	TextureModes;
	float   clipY, padf1, padf2, padf3;

	vec3	LightColor;
	int   	outNormalMap;
	vec3	LightDir;
	uint   pickId;

	
	vec4	TexMulFactor;
	vec4	TexAddFactor;

	vec4	ToonTexMulFactor;
	vec4	ToonTexAddFactor;

	vec4	SphereTexMulFactor;
	vec4	SphereTexAddFactor;

	uint 	Flag,passType,oit,rainbow;

	vec2  res,padf20;
	vec4  pad1, pad2, pad3;

 	MmdDynamicParam dp;

} ubo;

layout (binding = 2) uniform sampler2D Tex;
layout (binding = 3) uniform sampler2D ToonTex;
layout (binding = 4) uniform sampler2D SphereTex;
 
layout (binding = 5) uniform sampler2D ShadowTex;
 

float sdfact[3][3];

//SHADOW ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#define ambient 0.1


#ifdef IS_OIT


#include "oit_utils.h"


layout (early_fragment_tests) in;

layout(binding = 8, r32ui) uniform highp uimage2D counterImage;
//layout (offset = 0, binding = 0) uniform atomic_uint counter;
layout (std430,binding = 9) buffer CountBuffer 
{
	uint oit_counter;
	uint maxCount;  // OIT_LAYERS for interlock
	float minAlpha,pad;
	uint viewW;
	uint viewH;
	uint viewSize;
	uint pad2;
};

//coherent 
layout (std430, binding = 10) writeonly buffer oitData {
	OITData data[];
};

#else

#endif
layout (location = 0) out vec4 outFragColor;

float textureProj(vec4 shadowCoord, vec2 off)
{
	float shadow = 1.0;
	if ( shadowCoord.z > -1.0 && shadowCoord.z < 1.0 ) 
	{
		float dist = texture( ShadowTex, (vec2(1,-1)*shadowCoord.st+1)/2 + off ).r;
		if ( shadowCoord.w > 0.0 && dist < shadowCoord.z*0.9999995f )
		{
			shadow = ambient;
		}
	}
	return shadow;
}
#if			1
float filterPCF(vec4 sc)
{
	ivec2 texDim = textureSize(ShadowTex, 0);
	const float scale = 0.5;
	float dx = scale / float(texDim.x);
	float dy = scale / float(texDim.y);
	float shadowFactor = 0.0;
	int count = 0;
	int range = ubo.dp.shadowBlurCount;	
	for (int x = -range; x <= range; x++)	for (int y = -range; y <= range; y++)	{
			shadowFactor += textureProj(sc, vec2(dx*x, dy*y));
			count++;
	}	
	return shadowFactor / count;
}
#else
float blurknl2(in float x,in float y, in float s2)
{
	return 0.163*exp(-0.5*(x*x + y*y)/s2)/s2;
}
float filterPCF(vec4 sc)
{
	ivec2 texDim = textureSize(SphereTex, 0);
	float scale = 0.5;
	float dx = scale * 1.0 / float(texDim.x);
	float dy = scale * 1.0 / float(texDim.y);
	float shadowFactor = 0.0;
	int count = 0;
	const int range = 3;	
	for (int x = -range; x <= range; x++)	for (int y = -range; y <= range; y++)		{
			shadowFactor += textureProj(sc, vec2(dx*x, dy*y))*blurknl2(float(x),float(y),(range*range/4.0));
			count++;
	}
	return shadowFactor ;
}
#endif
//-SHADOW -----------------------------------------------------------




vec3 ComputeTexMulFactor(vec3 texColor, vec4 factor)
{
	vec3 ret = texColor * factor.rgb;
	return mix(vec3(1.0, 1.0, 1.0), ret, factor.a);
}

vec3 ComputeTexAddFactor(vec3 texColor, vec4 factor)
{
	vec3 ret = texColor + (texColor - vec3(1.0)) * factor.a ;
	ret = clamp(ret, vec3(0.0), vec3(1.0))+ factor.rgb;
	return ret;
}
float LinearizeDepth(float depth)
{
  float n = 100.0; // camera z near
  float f = 3000.0; // camera z far
  float z = depth;
  return 1-(2.0 * n) / (f + n - z * (f - n));	
}

void main() 
{	
	if (inPos.y< ubo.clipY) discard;
	int TexMode = ubo.TextureModes.x;
    int ToonTexMode = ubo.TextureModes.y;
    int SphereTexMode = ubo.TextureModes.z;
	bool sptexIsNormalMap=SphereTexMode==3;

#if 0
	if (ubo.outNormalMap!=0)	{
#if 1
		vec3 v=vec3(inNorV.x,-inNorV.y,ubo.outNormalMap==2?inNorV.z:	0);
		if (inNorV.x==0 && inNorV.y==0)		outFragColor=vec4(0.5,0.5,1,1);
		else		outFragColor=vec4((gl_FrontFacing?-v:v)/2+0.5,1);
	 	outFragColor.rgba=outFragColor.bgra;
#else
outFragColor=vec4(0,0,gl_FrontFacing?1:0,1);
#endif
		return ;
	}
#endif

	//outFragColor =texture(Tex, inUV)*1;	return;
#if ONLY_OUT_DEPTH
	float d= LinearizeDepth(gl_FragCoord.z);	outFragColor=vec4(d,d,d,1);	return ;
#endif

	//************************************************************************* START *************************************************************************
	vec3 lightDir = normalize(-ubo.LightDir);
	vec3 inNor=normalize(((!gl_FrontFacing) != (ubo.passType==0x10) ) ?- inNormal : inNormal);
	if (sptexIsNormalMap) 
	{  // SphereTex is normal map
	 
		vec3 normalMap = texture(SphereTex, inUV).rgb * 2.0 - 1.0; // Convert from [0,1] to [-1,1] range
			
		// Create TBN (Tangent, Bitangent, Normal) matrix for transforming normal from tangent to model space
		vec3 N = inNor;
		vec3 T = normalize(cross(vec3(0.0, 1.0, 0.0), N)); // Create tangent vector
		if (length(T) < 0.001) { // Handle case when normal is parallel to up vector
			T = normalize(cross(vec3(1.0, 0.0, 0.0), N));
		}
		vec3 B = normalize(cross(N, T)); // Create bitangent vector
			
		// Transform normal from tangent to model space
		mat3 TBN = mat3(T, B, N);
		inNor = normalize(TBN * normalMap);
	}

	vec3 nor=inNor;
	float NdotL=dot(nor, lightDir);
	float ln = NdotL;//(dot(nor, lightDir)+1)/2;//
		//smoothstep(-1,1,NdotL);
		//clamp(NdotL,0.0,1);
	//ln=max(0,ln);
	//ln = pow(ln,0.6f);
	ln = clamp(ln* ubo.lnv.x  + ubo.lnv.y, 0.0, 1.0);  // 0.5*(-1 ~ 1)+0.5
	float shadow=1.f;
	if (ubo.TextureModes.w==1) //shadow
	{
		vec4 suv= vec4(inPos,1)*ubo.g_mLightVP;
		if (suv.w>0.0) {
			shadow=   
		 	//  (texture(SphereTex, (vec2(1,-1)*suv.st/suv.w+1)/2).r<suv.z/suv.w?0.2:1);
		 	filterPCF(suv / suv.w);
			//color *=  shadow ;
			 //ln=ln*0.5+shadow*0.5;
 
			 //shadow=(0.25+0.75*shadow);//*(0.5+0.5*sin(inUV.x*100)*sin(inUV.y*100));
			 ln*=shadow;//clamp(shadow,0,1);
// #if USE_LIGHT_TEX
// 			 *(0.2f+0.8f*texture(LightTex, 2*(vec2(1,-1)*suv.st/suv.w+1)/2).r)
// #endif
			;
		}
		//outFragColor=vec4(ln,ln,ln,1);   return ;
	}

	vec3 color = vec3(0.0);
	float alpha = ubo.Diffuse.a;

	vec3 diffuseColor ;
	diffuseColor = ubo.Diffuse.rgb * ubo.LightColor;
	color = diffuseColor.rgb * ( ln*ubo.lnv.z + ubo.lnv.w )* 0.5  ;//normalize(inNor);//

	color += ubo.Ambient.rgb ;	
	color = clamp(color, 0.0, 1.0) ;

	 

	vec4 tc;
 
    if (TexMode != 0)
    {
		vec4 texColor =   texture(Tex, inUV);	////vec4(mod(inUV*10,1),0,1);//			outFragColor=texColor;   return ;
		//texColor.rgb=texColor.rgb*texColor.rgb*1.25;
		//texColor.rgb = ComputeTexMulFactor(texColor.rgb, ubo.TexMulFactor);
		//texColor.rgb = ComputeTexAddFactor(texColor.rgb, ubo.TexAddFactor);

	// if (bool(ubo.rainbow)){
	// 	float vellen = vtxpm.w/100.0;
	// 	vec3 hsl= RGBtoHSL(texColor.rgb);

	// 	// if (ubo.dp.colorMode==0x100)
	// 	// texColor.rgb= HSLtoRGB(vec3(fract(hsl.x-ubo.TexMulFactor.x+inUV.y/6.f),hsl.y*ubo.TexMulFactor.y,hsl.z*(ubo.TexMulFactor.z*0.5f+min(.5,vellen))));
	// 	// else 
	// 	if  (ubo.rainbow==0x10)
	// 	texColor.rgb= HSLtoRGB(vec3(fract(inUV.y),hsl.y*ubo.TexMulFactor.y,hsl.z*ubo.TexMulFactor.z));
	// 	else 
	// 	texColor.rgb=HSLtoRGB(vec3(fract(hsl.x+ubo.TexMulFactor.x),hsl.y*ubo.TexMulFactor.y,hsl.z*ubo.TexMulFactor.z));

	// }
 

        color *= texColor.rgb;



		if ((ubo.Flag & MMDSD_CTR_TRANSPARENT)!=0) 
		{
			alpha *=  smoothstep(0.1,0.15,length(gl_FragCoord.xy/ubo.res -vec2(0.5,0.5)));	
		}	


		if (TexMode == 2)
		{
			alpha *= texColor.a; //alpha=0.5;
		}
	 
		
    } 

	if (bool(ubo.rainbow)){
		float vellen = vtxpm.w/100.0;
		vec3 hsl= RGBtoHSL(color);

		// if (ubo.dp.colorMode==0x100)
		// texColor.rgb= HSLtoRGB(vec3(fract(hsl.x-ubo.TexMulFactor.x+inUV.y/6.f),hsl.y*ubo.TexMulFactor.y,hsl.z*(ubo.TexMulFactor.z*0.5f+min(.5,vellen))));
		// else 
		if  (ubo.rainbow==0x10)
		color= HSLtoRGB(vec3(fract(inUV.y),hsl.y*ubo.TexMulFactor.y,hsl.z*ubo.TexMulFactor.z));
		else 
		color=HSLtoRGB(vec3(fract(hsl.x+ubo.TexMulFactor.x),hsl.y*ubo.TexMulFactor.y,hsl.z*ubo.TexMulFactor.z)); 
 
	}
	if (alpha == 0.0)
	{
		discard;
	}
	if ((ubo.Flag & MMDSD_NO_COLOR)!=0) 
	{
		outFragColor=vec4(0,0,0,0);	
		return ;
	}

	vec3 specular;
	float specDotR=1.f;
#if 1
	//color-= pow(0.5-abs(dot(normalize(inEyePos-inPos),nor)),2)*0.33;
	vec3 p2e=inEyePos-inPos;
	if ((ubo.Flag & MMDSD_IS_CAMSB)!=0)
	{
		if (length(p2e)<100.f) discard;
	}
	vec3 norpos=normalize(p2e);
	float pp=1-clamp(dot(norpos,nor),0,1);	color = color- pp*ubo.dp.NdotEyeRat;
	if (ubo.Specular.a > 0.f)
	{
	#if 1
		vec3 halfVec = normalize((norpos+ lightDir)/2);
		vec3 specularColor = ubo.Specular.rgb * ubo.LightColor;
		specular = pow(max(0.0, dot(halfVec, nor)),ubo.Specular.a) * specularColor/1.6;
	#else
		vec3 reflection = reflect(  -lightDir,nor );
		specDotR=max(0, dot(normalize(reflection), norpos) );
  		specular =  (ubo.Specular.rgb )
		  //* ubo.LightColor  
		  * pow( specDotR,ubo.Specular.a)/2
		   ;

	#endif
		color+=specular*shadow;
		//color = color*(1-specular)+specular;
	}
	


#endif
#if 1
	if (ubo.TextureModes.w==1) {
		color*=( 1.f-ubo.dp.shadowRat+ubo.dp.shadowRat*shadow)
		*((1-ubo.dp.NdotLRat)+ubo.dp.NdotLRat*NdotL)
		*((1-ubo.dp.whiteRat)*color+ubo.dp.whiteRat) ;//shadow
		
		//color*=0.25+0.75*shadow;//shadow
	} 
	if (SphereTexMode != 0 && !sptexIsNormalMap) 
	{
		vec2 spUV = vec2(0.0); 
		spUV.x = inNorV.x * 0.5 + 0.5;
		spUV.y = inNorV.y * 0.5 + 0.5;
		vec3 spColor = texture(SphereTex, spUV).rgb ;// *ubo.Ambient.rgb * specDotR;
		//spColor = ComputeTexMulFactor(spColor, ubo.SphereTexMulFactor);
		//spColor = ComputeTexAddFactor(spColor, ubo.SphereTexAddFactor);
		if (SphereTexMode == 1)
		{
			color *= spColor;
		}
		else if (SphereTexMode == 2)
		{
			color += spColor;
		}
 

	}
	 
	
		
	
 #endif


 

#ifdef IS_OIT
	if (ubo.oit==1)
	{
	outFragColor = vec4(color, alpha);

	if (outFragColor.a<minAlpha ||oit_counter>OIT_BufSize-10240) {
			discard;
			return;
	}

		uint idx = atomicAdd(oit_counter,1) ;

		if (idx < OIT_BufSize) {

			ivec2 coord = ivec2(gl_FragCoord.xy);
			uint prev = imageAtomicExchange(counterImage, coord, idx);

	#if !PARAM_OIT
	
			outFragColor.rgb*= //(tex.w*outFragColor.a+1-tex.w)
				outFragColor.a//*tex.w
				;
				
	#endif
		#if USE_UINT_COLOR
			uvec4 colorTemp = uvec4(outFragColor * 255.0);
			//uint alpha = uint(0.75 * 255);
			uint color =  (colorTemp.a << 24 )
			// 0xFF000000
					|(colorTemp.b << 16) | (colorTemp.g << 8) | colorTemp.r;		
			data[idx].color = color;
		#else
			data[idx].color = outFragColor;
		#endif
			data[idx].depth = gl_FragCoord.z;
			data[idx].prev = prev;
		}
	discard;
	}
	else
	outFragColor = vec4(color, alpha);
#else	

	//color.r=float(gl_PrimitiveID)/100.0;

	//color=color*color*1.2;
	//color.xyz=nor/2+0.5;
	//color = inPos/1000.0;

	//outFragColor=vec4(1,1,0,1);
	outFragColor = vec4(color, alpha);
	
#endif



}
                                           