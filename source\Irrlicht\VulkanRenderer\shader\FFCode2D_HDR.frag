#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;
//precision mediump float;


layout (binding = 0) uniform UBO 
{
    float gamma;
	float exposure;
	int bit, pad3;

	vec4 pad[15 - 1];

	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w
} ubo;


layout (binding = 2)  uniform sampler2D g_tex0_sampler;


	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;

//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;


// BT.2020 EOTF constants
const float alpha = 1.0993;
const float beta = 0.0181;

//AI.Claude3.7 Transfer function for linear to BT.2020
vec3 linearToBT2020(vec3 linearColor) {
    vec3 result;
    
    // Apply transfer function for each RGB component
    for (int i = 0; i < 3; i++) {
        if (linearColor[i] <= beta) {
            result[i] = 4.5 * linearColor[i];
        } else {
            result[i] = alpha * pow(linearColor[i], 0.45) - (alpha - 1.0);
        }
    }
    
    return result;
}

//AI.DeepSeek R1
float adjustLevel(float c, float inMin, float inMid, float inMax) {
    if (inMax <= inMin) {
        return c >= inMax ? 1.0 : 0.0;
    }
    c = clamp(c, inMin, inMax);
    float normalized = (c - inMin) / (inMax - inMin);
    float midNorm = (inMid - inMin) / (inMax - inMin);
    midNorm = clamp(midNorm, 1e-5, 1.0 - 1e-5); // 避免log(0)
    float gamma = log(0.5) / log(midNorm);
    return pow(normalized, 1.0 / gamma);
}

// adding pixel shader
void main()
{


	vec4 col=texture(g_tex0_sampler,i_tex0);
#if 0
    col.r = adjustLevel(col.r, 0.0, 0.33, 1.0);
    col.g = adjustLevel(col.g, 0.0, 0.33, 1.0);
    col.b = adjustLevel(col.b, 0.0, 0.33, 1.0);
#endif 

#if 0
    outFragColor = vec4(linearToBT2020(col.rgb*ubo.exposure),col.a);
#else
#if 0
	outFragColor =   vec4(col.rgb/(col.rgb+1),col.a); 
#elif 0

    const float a = 2.51f;
    const float b = 0.03f;
    const float c = 2.43f;
    const float d = 0.59f;
    const float e = 0.14f;
    outFragColor= vec4(clamp((col.rgb*(a*col.rgb+b))/(col.rgb*(c*col.rgb+d)+e),0,1),col.a);
#elif 0
    // 曝光色调映射
    vec3 mapped = vec3(1.0) - exp(-col.rgb );
    // Gamma校正 
    outFragColor = vec4(mapped,col.a);
#elif 1
    // 曝光色调映射
    vec3 mapped = vec3(1.0) - exp(-col.rgb * ubo.exposure);
    // Gamma校正 
#if 1
    outFragColor = vec4(pow(mapped, vec3(1.0 / ubo.gamma)),col.a);
#else
    #if 0
    if (i_tex0.x>0.5)
        outFragColor = vec4(pow(mapped, vec3(1.0 / ubo.gamma)),col.a);
    else 
        outFragColor=vec4(i_tex0.x,i_tex0.x,i_tex0.x,1) ;

    if (i_tex0.y>0.5)
    {
        ivec4 ic= ivec4(outFragColor*ubo.bit);
        outFragColor= 0.25+vec4(ic)/ubo.bit/8;
   
    }
    #else
    float g2=ubo.gamma*2;   outFragColor = vec4(i_tex0.x*g2,i_tex0.y>0.5?(i_tex0.y-0.5)*2*g2:0,i_tex0.y>0.75?(i_tex0.y-0.75)*4*g2:0,0);
    #endif
#endif
    //float g2=i_tex0.x*ubo.gamma;  outFragColor = vec4(g2,g2,g2,col.a);
#elif 1
    // 曝光色调映射
    vec4 mapped = vec4(1.0) - exp(-col * ubo.exposure);
    // Gamma校正 
    outFragColor = pow(mapped, vec4(1.0 / ubo.gamma));
    //outFragColor.a = col.a;
    //float cmax= max(col.r,max(col.g,col.b));    float mul= 255.0/cmax;
    
    //outFragColor = (col.a>0)?vec4(outFragColor.rgb/col.a,col.a):vec4(0);
#else
	outFragColor = vec4(vec3(1.0)-exp(-col.rgb*0.5),col.a);

#endif
#endif

    
}