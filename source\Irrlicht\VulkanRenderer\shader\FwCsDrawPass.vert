#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
#include "GsParticleShared.h"
#include "colorutil.glsl"
#define CLOSE_DIS_CLIP 0

#define IS_VS_PASS
#include "FwParticleShaderShared.glsl"

layout(location = 0) out	vec4 color;
layout(location = 1) out	vec4 tex;
layout(location = 2) out	vec4 fv;
#if FW_USE_SHADOWMAP
layout(location = 3) out	vec4 outpos; 
#endif
#if FW_USE_SHADOWMAPx
layout(location = 3) out	vec4 texDepth;// all interpolation
#endif
void main()
{
#if 1
	uint id=(gl_VertexIndex/4) * vertKldMul+vertKldAdd;
 	vec4 op =  vtxs[id].outpos;
#if FW_USE_SHADOWMAP
	outpos=vec4(op.xyz,vtxs[id].text0.x); 
#endif
 	vec4 center= vec4(op.xyz,1.0)*mV;
	
	vec2 vtxpos = vec2((gl_VertexIndex & 2) >> 1,((gl_VertexIndex + 3) & 2) >> 1);//0,1  0,0  1,0  1,1
	color=vtxs[id].color ;
#if CLOSE_DIS_CLIP
	const float maxClipDis=500.f;
	if (center.z<maxClipDis) color.a*= (center.z-50.f)/(maxClipDis-50.f);
#endif
	fv=vtxs[id].fv;
	tex = vec4(vtxpos, vtxs[id].text0.zw);
	vec4 pos=(vec4(vec3( vec2(2.f,-2.f)*vtxpos.xy+vec2(-1.f,1.f), 0) * op.w, 0.0) + center );

#if CLOSE_DIS_CLIP
	if (center.z<50.f) gl_Position=vec4(-2.f,0,0,1.f);
	else gl_Position =  pos*mP;
#else
	gl_Position =  pos*mP;
#endif
#if FW_USE_SHADOWMAPx
	if (useShadowMap==1)
	{
		vec4 center= vec4(op.xyz,1.0)*mLightV;
		vec4 pos=(vec4(vec3( vec2(2.f,-2.f)*vtxpos.xy+vec2(-1.f,1.f), 0) * op.w, 0.0) + center );
		texDepth = pos*mLightP;
	}
	else 
	texDepth=vec4(0);
	//texDepth.xy/=texDepth.w;
#endif
#else

	gl_Position =  vtxs[gl_VertexIndex].outpos;
	color=vtxs[gl_VertexIndex].color;
	tex=vtxs[gl_VertexIndex].text0;
	fv=vtxs[gl_VertexIndex].fv;

#endif

//gl_PointSize = 5;
}

