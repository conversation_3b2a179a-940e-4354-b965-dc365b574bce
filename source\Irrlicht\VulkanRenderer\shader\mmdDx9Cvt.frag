#version 450

layout(constant_id = 1231) const uint point_mode = 0u;
layout(constant_id = 2) const bool s0_bound = true;
layout(constant_id = 3) const bool s1_bound = true;
layout(constant_id = 4) const bool s2_bound = true;
layout(constant_id = 5) const bool s3_bound = true;
layout(constant_id = 6) const bool s4_bound = true;
layout(constant_id = 7) const bool s5_bound = true;
layout(constant_id = 8) const bool s6_bound = true;
layout(constant_id = 9) const bool s7_bound = true;
layout(constant_id = 1230) const uint pixel_fog_mode = 0u;
layout(constant_id = 1228) const bool fog_enabled = false;
layout(constant_id = 1225) const bool alpha_test = false;
layout(constant_id = 1226) const uint alpha_func = 7u;

layout(set = 0, binding = 1, std140) uniform D3D9FixedFunctionPS
{
    vec4 textureFactor;
} consts;

layout(set = 0, binding = 10, std140) uniform D3D9SharedPS
{
    vec4 _m0;
    vec2 _m1;
    vec2 _m2;
    float _m3;
    float _m4;
    vec4 _m5;
    vec2 _m6;
    vec2 _m7;
    float _m8;
    float _m9;
    vec4 _m10;
    vec2 _m11;
    vec2 _m12;
    float _m13;
    float _m14;
    vec4 _m15;
    vec2 _m16;
    vec2 _m17;
    float _m18;
    float _m19;
    vec4 _m20;
    vec2 _m21;
    vec2 _m22;
    float _m23;
    float _m24;
    vec4 _m25;
    vec2 _m26;
    vec2 _m27;
    float _m28;
    float _m29;
    vec4 _m30;
    vec2 _m31;
    vec2 _m32;
    float _m33;
    float _m34;
    vec4 _m35;
    vec2 _m36;
    vec2 _m37;
    float _m38;
    float _m39;
} D3D9SharedPS_1;

layout(push_constant, std430) uniform render_state_t
{
    vec3 fog_color;
    float fog_scale;
    float fog_end;
    float fog_density;
    float alpha_ref;
} render_state;

layout(set = 0, binding = 2) uniform sampler2D s0;
layout(set = 0, binding = 3) uniform sampler2D s1;
layout(set = 0, binding = 4) uniform sampler2D s2;
layout(set = 0, binding = 5) uniform sampler2D s3;
layout(set = 0, binding = 6) uniform sampler2D s4;
layout(set = 0, binding = 7) uniform sampler2D s5;
layout(set = 0, binding = 8) uniform sampler2D s6;
layout(set = 0, binding = 9) uniform sampler2D s7;

layout(location = 0) in vec4 in_Texcoord0;
layout(location = 1) in vec4 in_Texcoord1;
layout(location = 2) in vec4 in_Texcoord2;
layout(location = 3) in vec4 in_Texcoord3;
layout(location = 4) in vec4 in_Texcoord4;
layout(location = 8) in vec4 in_Texcoord5;
layout(location = 9) in vec4 in_Texcoord6;
layout(location = 10) in vec4 in_Texcoord7;
layout(location = 5) in vec4 in_Color0;
layout(location = 6) in vec4 in_Color1;
layout(location = 11) in float in_Fog0;
layout(location = 0) out vec4 out_Color0;
vec4 _147;

void main()
{
    vec4 _23 = vec4(gl_PointCoord, 0.0, 0.0);
    bvec4 _32 = bvec4(bitfieldExtract(point_mode, 1, 1) == 1u);
    vec4 toon = texture(s0, mix(in_Texcoord0, _23, _32).xy);
    vec4 toon1 = mix(vec4(0.0, 0.0, 0.0, 1.0), toon, bvec4(s0_bound));
    vec4 colt = toon1 * in_Color0;

    vec4 colta = vec4(colt.x, colt.y, colt.z, in_Color0.w);
    vec4 texCol = texture(s1, mix(in_Texcoord1, _23, _32).xy);
    vec4 colTT = mix(vec4(0.0, 0.0, 0.0, 1.0), texCol, bvec4(s1_bound)) * vec4(colta.x, colta.y, colta.z, (toon1 * in_Color0).w);
    vec4 sph = texture(s2, mix(in_Texcoord2, _23, _32).xy);
    vec4 sph1 = mix(vec4(0.0, 0.0, 0.0, 1.0), sph, bvec4(s2_bound));
    vec4 cs = clamp(sph1 + colTT, vec4(0.0), vec4(1.0));
    vec4 csa = vec4(cs.x, cs.y, cs.z, colTT.w);
    vec4 _129 = vec4(csa.x, csa.y, csa.z, (sph1 * colTT).w) + (in_Color1 * vec4(1.0, 1.0, 1.0, 0.0));




    _147 = _129;
    if (fog_enabled)
    {
        float _151 = gl_FragCoord.z * (1.0 / gl_FragCoord.w);
        float _167;
        switch (pixel_fog_mode)
        {
            case 1u:
            {
                _167 = exp(-(_151 * render_state.fog_density));
                break;
            }
            case 2u:
            {
                float _160 = _151 * render_state.fog_density;
                _167 = exp(-(_160 * _160));
                break;
            }
            case 3u:
            {
                float _165 = (render_state.fog_end - _151) * render_state.fog_scale;
                float _204 = isnan(0.0) ? _165 : (isnan(_165) ? 0.0 : max(_165, 0.0));
                _167 = isnan(1.0) ? _204 : (isnan(_204) ? 1.0 : min(_204, 1.0));
                break;
            }
            default:
            {
                _167 = in_Fog0;
                break;
            }
        }
        vec3 _170 = mix(render_state.fog_color, _129.xyz, vec3(_167));
        _147 = vec4(_170.x, _170.y, _170.z, _129.w);
    }
    out_Color0 = _147;
    if (alpha_test)
    {
        bool _201;
        switch (alpha_func)
        {
            case 0u:
            {
                _201 = false;
                break;
            }
            case 1u:
            {
                _201 = out_Color0.w < render_state.alpha_ref;
                break;
            }
            case 2u:
            {
                _201 = out_Color0.w == render_state.alpha_ref;
                break;
            }
            case 3u:
            {
                _201 = out_Color0.w <= render_state.alpha_ref;
                break;
            }
            case 4u:
            {
                _201 = out_Color0.w > render_state.alpha_ref;
                break;
            }
            case 5u:
            {
                _201 = out_Color0.w != render_state.alpha_ref;
                break;
            }
            case 6u:
            {
                _201 = out_Color0.w >= render_state.alpha_ref;
                break;
            }
            default:
            {
                _201 = true;
                break;
            }
        }
        if (!_201)
        {
            discard;
        }
    }
}

