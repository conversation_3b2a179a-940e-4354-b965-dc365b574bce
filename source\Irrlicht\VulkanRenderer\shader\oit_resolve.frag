#version 460

precision highp float;

#extension GL_GOOGLE_include_directive : enable
#include "oit_utils.h"
#extension GL_ARB_explicit_attrib_location : require
#extension GL_ARB_shader_image_load_store : require
#extension GL_ARB_shader_storage_buffer_object : require



layout (early_fragment_tests) in;
layout (set = 0,binding = 7, r32ui) uniform  uimage2D counterImage;
layout (std430, binding = 10) readonly buffer oitData {
	OITData data[];
};

layout (std430,binding = 8) buffer CountBuffer 
{
	uint oit_counter;
	uint maxCount;  // OIT_LAYERS for interlock
	float minAlpha,pad;
	uint viewW;
	uint viewH;
	uint viewSize;
	uint pad2;
};
layout(location = 0)  out  vec4 colorOut;

#define ONLY_SORT_IDX  1

float transferHDR(float e)
{
	const float a=1.0993,b=0.0181;
	if (e>b) return a*pow(e,0.45)-(a-1);
	else return e*0.45;
}

void main(void)
{
	ivec2 coord = ivec2(gl_FragCoord.xy);
	uint idx = imageLoad(counterImage, coord).x;

 

	uint count = 0u;
	// farthest (largest depth value) first

// //debug
// 	if (idx!=0)			colorOut = vec4(1.0, 0.0, 0.0, 0.0);	
// 	else {
// 		if (oit_counter>10000)		colorOut = vec4( float(oit_counter)/100000.f, 0.0, 1.0, 0.0);
// 		else	colorOut = vec4(float(oit_counter)/100000.f, 1.0, 0.0, 0.0);
// 	 	return;
// 	}

#if ONLY_SORT_IDX

	colorOut = vec4(0.0, 0.0, 0.0, 0.0);

	uint sorted[64];

	while (idx != 0) {

		int insertPoint = int(count);
		float cdepth=data[idx].depth;
		while (insertPoint > 0) {
			if (data[sorted[insertPoint - 1]].depth <  cdepth) {
				break;
			}

			sorted[insertPoint] = sorted[insertPoint - 1];

			insertPoint--;
		}

		if (insertPoint < maxCount) {
		sorted[insertPoint] = idx;
		}

		if (count < maxCount) {
		count++;
		}
		//else {			colorOut =  vec4(1,0,0,1);			return;		}

		idx =  data[idx].prev;
	}


  // if (count == maxCount) colorOut = vec4(1.0, 0.0, 0.0, 1.0); else
	
	for (uint i = 0; i < count; i++) //for (int i = int(count)-1; i>=0; i--) //
	{
		
		
#if USE_UINT_COLOR
		uint n = data[sorted[count - 1 - i]].color;
		vec4 tc = vec4( n&0xff, (n>>8)&0xff, (n>>16)&0xff,(n>>24) )/255.0;
#else
		vec4 tc = data[sorted[count - 1 - i]].color;
#endif

#if PARAM_OIT
		float a2=pow(tc.a,PARAM_OIT_P1) ;
		colorOut = vec4( tc.rgb
		*a2 *3
		*(i)/count
		+  colorOut.rgb*(1-a2)
		,
		a2+colorOut.a*(1-a2));
#elif IS_OIT_ADDCOLOR
colorOut = vec4( tc.rgb	+  colorOut.rgb,		tc.a+colorOut.a*(1-tc.a));		
#else // normal

colorOut = vec4( (tc.rgb // *1.5//a2 
		+  colorOut.rgb*(1-tc.a) ),
		tc.a+colorOut.a*(1-tc.a));
		//if (colorOut.a>.99999999) break;
#endif
		//colorOut = vec4( tc.rgb  +  colorOut.rgb,tc.a+colorOut.a*(1-tc.a));
	//if (colorOut.a>0.999)			break;
	}

	 //colorOut.rgb=colorOut.rgb/(1+colorOut.rgb);

//colorOut.rgb*=2;

  //colorOut=vec4(1,0,0,0);
 //colorOut.rgb=vec3(transferHDR(colorOut.r),transferHDR(colorOut.g),transferHDR(colorOut.b));

//______________________________________________________________________________________________________________

#else  // ONLY_SORT_IDX==0

	OITData sorted[maxCount];

	while (idx != 0) {
		OITData candidate = data[idx];

		int insertPoint = int(count);
		while (insertPoint > 0) {
			if (sorted[insertPoint - 1].depth < candidate.depth) {
				break;
			}

			sorted[insertPoint] = sorted[insertPoint - 1];

			insertPoint--;
		}

		if (insertPoint < maxCount) {
		sorted[insertPoint] = candidate;
		}

		if (count < maxCount) {
		count++;
		}

		idx = candidate.prev;
	}

	colorOut = vec4(0.0, 0.0, 0.0, 0.0);

	for (uint i = 0; i < count; i++) 
	{
		
#if USE_UINT_COLOR
		uint n = sorted[count - 1 - i].color;
		// uvec4 temp;
		// temp.r = n % 256u;		n = n / 256u;
   		// temp.g = n % 256u;		n = n / 256u;
		// temp.b = n % 256u;		n = n / 256u;
		// temp.a = n;
		// vec4 tc = vec4(temp) / vec4(255.0);
		vec4 tc = vec4( n&0xff, (n>>8)&0xff, (n>>16)&0xff,(n>>24) )/255.0;
#else
		vec4 tc = sorted[count - 1 - i].color;
#endif

		//tc *=vec4(2,2,2,1);
		//colorOut =  mix(colorOut, tc, tc.a);
		//vec4(mix(colorOut.rgb, tc.rgb, tc.a),tc.a+colorOut.a*(1-tc.a));
		colorOut = vec4( tc.rgb+  colorOut.rgb*(1-tc.a),
		1
		//tc.a+colorOut.a*(1-tc.a)
		);
		//colorOut = vec4( tc.rgb  +  colorOut.rgb,tc.a+colorOut.a*(1-tc.a));
	//if (colorOut.a>0.999)			break;
	}
#endif
}
