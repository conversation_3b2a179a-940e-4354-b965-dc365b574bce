#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
#extension GL_ARB_explicit_attrib_location : require
#extension GL_ARB_shader_atomic_counters : require
#extension GL_ARB_shader_image_load_store : require
#extension GL_ARB_shader_storage_buffer_object : require

// Fragment shader interlock extensions (optional)
#ifdef GL_NV_fragment_shader_interlock
#extension GL_NV_fragment_shader_interlock : enable
layout(pixel_interlock_ordered) in;
#define INTERLOCK_SUPPORTED 1
#define beginInvocationInterlock beginInvocationInterlockNV
#define endInvocationInterlock endInvocationInterlockNV
#elif defined(GL_ARB_fragment_shader_interlock)
#extension GL_ARB_fragment_shader_interlock : enable
layout(pixel_interlock_ordered) in;
#define INTERLOCK_SUPPORTED 1
#define beginInvocationInterlock beginInvocationInterlockARB
#define endInvocationInterlock endInvocationInterlockARB
#else
#define INTERLOCK_SUPPORTED 0
#endif

#define FW_OIT 2


#include "FwPtcGLSL_FRAG.glsl"



