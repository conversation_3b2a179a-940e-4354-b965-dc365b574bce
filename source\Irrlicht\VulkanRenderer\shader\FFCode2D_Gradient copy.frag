#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;
#include "colorutil.glsl"

#define PI 3.1415927

layout (binding = 0) uniform UBO 
{


	int type[4];
	int func[4];
	int anti[4] ;
	float xmin,xmax,ymin,ymax;
	vec4 fv0, fv1;
	vec4 color0,color1;
	vec4 septs[4];
	vec2 texWH,pad_1;
	float angles_X_[4];
	vec4 pad[15 - 14];

	vec2 res;
	float resWdH;//w / h
	float resHdW;//h/w
};

layout (binding = 2)  uniform sampler2D g_tex0_sampler;
layout (binding = 3)  uniform sampler2D g_tex1_sampler;

layout(location = 0) in vec4 i_colorD;
layout(location = 1) in vec2 i_tex;

//========================================= PS ========================================
layout (location = 0) out vec4 outFragColor;


float getRatioBetween2Points(vec2 spt,vec2 ept)
{
	vec2 c= i_tex*texWH-spt;
	vec2 v= ept-spt;
	float d=length(v);
	if (d==0.0) return 0.0;
	
	return dot(v/d,c)/d;
}

float shapeFunction(int func, float x)
{
	switch (func)
	{
		default: return x;
		case 1: return sin(PI/2*x);
	}
}

float funcOnType(int dirType, int func, int anti,vec2 spt,vec2 ept)
{
	float x;
	switch (dirType)
	{
		default: 	x=0.0;			break;
		case 1:		x=clamp((i_tex.x - xmin) / (xmax - xmin), 0.0, 1.0);	break;
		case 2:		x=clamp((i_tex.y - ymin) / (ymax - ymin), 0.0, 1.0);	break;
		case 3:		x=smoothstep(xmin,xmax,i_tex.x);		break;
		case 4:		x=smoothstep(ymin,ymax,i_tex.y);		break;

		case 8:  case 9:   x=clamp(getRatioBetween2Points(spt,ept), 0.0, 1.0);break;
	}
	if (anti==1) x=1.0-x;


	return shapeFunction(func,x);
}

void main()
{

	vec4 c0 = vec4(1,1,1,1);;//color0;
	c0.a= texture(g_tex0_sampler,i_tex).a; 

	//vec4 c1 =  texture(g_tex1_sampler,i_tex0); 
	vec3 HSL = RGBtoHSL(c0.rgb);
	vec3 hsladd;
	
	//hsladd=vec3(i_tex.x/10,1,0.7-0.2*i_tex.y);
	float factH= 		funcOnType(type[0],func[0],anti[0],septs[0].xy,septs[0].zw);
	vec2 factSL = vec2(	funcOnType(type[1],func[1],anti[1],septs[1].xy,septs[1].zw),
						funcOnType(type[2],func[2],anti[2],septs[2].xy,septs[2].zw) );
	hsladd.x= fract(HSL.x+ mix(fv0.x,fv1.x,factH));
	hsladd.yz= clamp(HSL.yz*mix(fv0.yz,fv1.yz,factSL),0,1);
	//hsladd.z= (HSL.z+ fv0.z*(1.0-factL)+fv1.z*factL);
	c0.rgb=HSLtoRGB(hsladd);
	outFragColor= c0;
	//vec4( 1,0,0,1);

}