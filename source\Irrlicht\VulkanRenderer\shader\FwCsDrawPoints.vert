//Draw particle as point list
#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
#include "GsParticleShared.h"
#include "colorutil.glsl"

#define IS_VS_DRAW_POINTLIST 
#include "FwParticleShaderShared.glsl"

layout(location = 0) out	vec4 color;
layout(location = 1) out	vec4 tex;
layout(location = 2) out	vec4 fv;

void main()
{

	VSParticle ptc=PtcBuf[ids[gl_VertexIndex]];
	uint eid = ptc.eid;
	Ember emb = Emb[ptc.eid];
	float ratioO = ptc.life > 0.00001f ? ptc.timer / ptc.life : 1.0f;
	//ratioO=clamp(ratioO,0,1);
	
#if 1
	vec4 c ;
	uint tid = (emb.tflag & TFLAG_ColMaskIds);
	if (bool(tid))
	{
		float ratio = ratioO;
		float ratioInv = 1 - ratio;
		if (bool(emb.tflag & TFLAG_ColMaskInv))
		{
			float t = ratioInv; ratioInv = ratio; ratio = t;
		}
		float rndOfx = (bool(emb.tflag&TFLAG_ColMaskRandom) ? ptc.ofs.x : 0.f);
        float ratioT = ratioInv * emb.fCol2.b;
		if (bool(emb.tflag & TFLAG_ColHueMask))
		{
			vec3 hsl = (RGBtoHSL(ptc.col.rgb));
			switch (tid)
			{
				case TFLAG_ColHue:
				{
                    hsl = vec3(fract(hsl.r + emb.fCol2.r + fract(ratioT) * emb.fCol2.a + rndOfx), hsl.g * emb.fCol2.g, hsl.b);
					break;
				}
				case TFLAG_ColHueSin:
				{
                    hsl = vec3(fract(hsl.r + sin(2 * PI * (emb.fCol2.r + ratioT + rndOfx)) * emb.fCol2.a), hsl.g * emb.fCol2.g, hsl.b);
					break;
				}
				case TFLAG_ColLight:
				{
                    hsl = vec3(hsl.r, hsl.g, clamp(hsl.b * emb.fCol2.a + hsl.b * emb.fCol2.r * fract(ratioT + rndOfx) + (1 - hsl.b) * emb.fCol2.g * fract(ratioT + rndOfx),0.f,1.f));
					break;
				}
				case TFLAG_ColLightSin:
				{
                    float sn = (1.f - cos(2.f * PI * (ratioT + rndOfx))) / 2;
					hsl = vec3(hsl.r, hsl.g, clamp(hsl.b*emb.fCol2.a+(hsl.b*emb.fCol2.r + (1 - hsl.b)*emb.fCol2.g)*sn ,0.f,1.f));

					break;
				}
			}
			c = vec4(HSLtoRGB(hsl), ptc.col.a);
		}
		else
		{
			switch (tid)
			{
			default:
			case 0:
			{
				c = ptc.col;
				break;
			}
			case TFLAG_ColToN:
			{
				c = ptc.col *ratio + emb.fCol2 * ratioInv;
				break;
			}


			}
		}
	}
	else
	{
		c = ptc.col;
	}
#else

	vec4 c = ptc.col;
#endif
	float r = CalcRatio(ratioO, uint(emb.mpc));
	//if (ptc.timer <= 0.f) ptc.col = 0;  //first emb timer=0 use 0 col, then do not need this, and this will discard last 
#if HAS_BAND_V
	if ((emb.tflag & TFLAG_OnBandV_Mask)!=0u && ptc.bid < PT_BANDS ) {
		vec4 bv = bandv[ptc.bid];

		vec4 bdRate = vec4(
			float((emb.tflag & TFLAG_OnBandV_H) >> 24),
			float((emb.tflag & TFLAG_OnBandV_S) >> 20),
			float((emb.tflag & TFLAG_OnBandV_L) >> 16),
			float((emb.tflag & TFLAG_OnBandV_A) >> 28)
			)/15.f;

		vec3 hsl = (RGBtoHSL(c.rgb));
		hsl *= (1.f - bdRate.xyz + bdRate.xyz * bv.x);  
		c.rgb = HSLtoRGB(hsl);
		c.a *= (1.f - bdRate.a + bdRate.a * bv.x);
		//output.color *= (1.f - bdRate + bdRate *bv.x);

	}
#endif


	//c.a = r * c.a;


	float radius = (( emb.cflag & CFLAG_SpcOnZ)!=0u ? ptc.ofs.z : 1.f)*emb.r *	CalcRatio(ratioO, uint(emb.mpr));// *fireScale;

	



	//GS===========

	vec4 center =  vec4(ptc.pos, 1.0) * mW;
	bool needOut = true;
	radius *=  stdDistance/length( center) *mWScale;

	//c=vec4(radius,0,0,1);
	color = c;

	vec2 fa = vec2(Emb[eid].texId, float(Emb[eid].cflag & 1U));

	tex = vec4(0,0, fa.xy);
	fv=vec4(1-ratioO,ratioO,ptc.life,ptc.ofs.x);;
	gl_Position =  center * mV*mP;
	gl_PointSize = radius*2;

}

