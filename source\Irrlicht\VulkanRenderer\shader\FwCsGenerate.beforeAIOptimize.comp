#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
#include "GsParticleShared.h"



layout (local_size_x = 1) in;
//#include "colorutil.glsl"
#define IS_CS 1
#include "FwParticleShaderShared.glsl"



void GenParticle(VSParticle pt)
{
	if (freeCount<CS_LOCAL_SIZE_X*10)
		return;

	int freeIdx= int(atomicAdd(freeCount,-1))-1;
	uint newShowIdx=atomicAdd(showCountOut,1);

	if (freeIdx<CS_LOCAL_SIZE_X || newShowIdx>=MaxPtcCount)
	{
		atomicAdd(freeCount,1);
		atomicAdd(showCountOut,-1);
		memoryBarrier();
		return;
	}
	uint ffIdx=atomicAdd(freeB,1);
	uint pid= FreeIds[ffIdx%MaxPtcCount];

	Pts[pid]= pt;
	
	idsOut[newShowIdx]=pid;
	memoryBarrier();
}




void main() 
{
	// Current SSBO index
	
#if 0
	uint i = gl_GlobalInvocationID.x;
	if (i<GenCount)
	{
		GenParticle(PtsGen[i]);
	}
#else
	uint gc= min(GenCount, MaxPtcCount- showCountOut);

	for (int i=0;i<gc && freeCount>CS_LOCAL_SIZE_X;i++) 
	{
		freeCount -= 1;
		uint newShowIdx=showCountOut;
		showCountOut+=1;

		uint ffIdx=freeB;
		freeB+=1;
		uint pid= FreeIds[ffIdx%MaxPtcCount];

		Pts[pid]= PtsGen[i];
		
		idsOut[newShowIdx]=pid;
	}
	memoryBarrier();

	drawIdxCmd = GlslVkDrawIndexedIndirectCommand(showCountOut*6*drawIdxCmdKld,1, 0,0,0);

	drawNoIdxCmd= GlslVkDrawIndirectCommand(showCountOut,1,0,0); // POINT LIST
	dsipCmd = GlslVkDispatchIndirectCommand( uint( ceil( float(showCountOut)/float(CS_LOCAL_SIZE_X))),1,1);
	//dsipCmd = GlslVkDispatchIndirectCommand( uint(ceil(1.0 )),1,1);
	//showCount = showCountOut;
	atomicExchange(showCount,showCountOut);
	atomicExchange(showCountOut,0);
	totalCount=freeCount+showCount;
	freeB = freeB % MaxPtcCount;
	freeF = freeF % MaxPtcCount;
	memoryBarrier();//memoryBarrierBuffer();
#endif
	//test codes
	//idrCmd[0].indexCount+=1;
	//idrCmd[1].indexCount+=1;
	//maxCount=MaxPtcCount;



}