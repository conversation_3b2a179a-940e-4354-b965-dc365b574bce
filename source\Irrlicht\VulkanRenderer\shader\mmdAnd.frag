#version 460

#extension GL_ARB_separate_shader_objects : enable
#extension GL_ARB_shading_language_420pack : enable


layout (location = 0) in vec3 inPos;
layout (location = 1) in vec3 inNor;
layout (location = 2) in vec3 inEyePos;
layout (location = 3) in vec2 inUV;
layout (location = 4) in vec2 inNorV;
layout (binding = 1) uniform UBO 
{
	mat4 	g_mView;
		mat4    g_mLightVP;
	vec4	Diffuse;
	vec4	Ambient;
	vec4	Specular;	
	vec4	lnv;

	vec3	LightColor;
	float   pad00;
	vec3	LightDir;
	float   pad01;

	
	vec4	TexMulFactor;
	vec4	TexAddFactor;

	vec4	ToonTexMulFactor;
	vec4	ToonTexAddFactor;

	vec4	SphereTexMulFactor;
	vec4	SphereTexAddFactor;

	ivec4	TextureModes;
	

} ubo;

layout (binding = 2) uniform sampler2D Tex;
layout (binding = 3) uniform sampler2D ToonTex;
layout (binding = 4) uniform sampler2D SphereTex;

layout (location = 0) out vec4 outFragColor;

vec3 ComputeTexMulFactor(vec3 texColor, vec4 factor)
{
	vec3 ret = texColor * factor.rgb;
	return mix(vec3(1.0, 1.0, 1.0), ret, factor.a);
}

vec3 ComputeTexAddFactor(vec3 texColor, vec4 factor)
{
	vec3 ret = texColor + (texColor - vec3(1.0)) * factor.a ;
	ret = clamp(ret, vec3(0.0), vec3(1.0))+ factor.rgb;
	return ret;
}

void main() 
{
	//outFragColor =texture(Tex, inUV)*1;	return;

	vec3 lightDir = normalize(-ubo.LightDir);

	vec3 nor=normalize(inNor);
	float ln = dot(nor, lightDir);
	//ln=max(0,ln);
	ln = clamp(ln* ubo.lnv.x  + ubo.lnv.y, 0.0, 1.0);
	vec3 color = vec3(0.0);
	float alpha = ubo.Diffuse.a;

	vec3 diffuseColor = ubo.Diffuse.rgb * ubo.LightColor;
	color = diffuseColor.rgb * ( ln*ubo.lnv.z + ubo.lnv.w )* ubo.lnv.x   ;//normalize(inNor);//
	color += ubo.Ambient.rgb ;
	color = clamp(color, 0.0, 1.0) ;

    int TexMode = ubo.TextureModes.x;
    int ToonTexMode = ubo.TextureModes.y;
    int SphereTexMode = ubo.TextureModes.z;





	if (ToonTexMode != 0)
	{
		vec4 tc = texture(ToonTex, vec2(0.0, 1-ln));
		//vec3 toonColor = ComputeTexMulFactor(tc.rgb, ubo.ToonTexMulFactor);
		//toonColor = ComputeTexAddFactor(toonColor, ubo.ToonTexAddFactor);
		color *=  tc.rgb;// toonColor;
		//alpha*=tc.a;
	}
	
    if (TexMode != 0)
    {
		vec4 texColor = texture(Tex, inUV);
		//texColor.rgb=texColor.rgb*texColor.rgb*1.5;
		//texColor.rgb = ComputeTexMulFactor(texColor.rgb, ubo.TexMulFactor);
		//texColor.rgb = ComputeTexAddFactor(texColor.rgb, ubo.TexAddFactor);
        color *= texColor.rgb;
		if (TexMode == 2)
		{
			alpha *= texColor.a; //alpha=0.5;
		}
    }
	if (alpha == 0.0)
	{
		discard;
	}


	vec3 specular;
	float specDotR=1.f;
#if 1
	color-= pow(1-abs(dot(normalize(inEyePos-inPos),nor)),2)*0.33;
	if (ubo.Specular.a > 0.f)
	{
	#if 0
		vec3 halfVec = normalize((normalize(inEyePos-inPos) + lightDir)/2);
		vec3 specularColor = ubo.Specular.rgb * ubo.LightColor;
		specular = pow(max(0.0, dot(halfVec, inNor)),ubo.Specular.a) * specularColor;
	#else
		vec3 reflection = reflect(  -lightDir,nor );
		specDotR=max(0, dot(normalize(reflection), normalize(inEyePos-inPos)) );
  		specular =  (ubo.Specular.rgb )
		  //* ubo.LightColor  
		  * pow( specDotR,ubo.Specular.a)
		   ;

	#endif
		color+=specular;
		//color = color*(1-specular)+specular;
	}

#endif
#if 0
	if (SphereTexMode != 0)
	{
		vec2 spUV = vec2(0.0); 
		spUV.x = inNorV.x * 0.5 + 0.5;
		spUV.y = inNorV.y * 0.5 + 0.5;
		vec3 spColor = texture(SphereTex, spUV).rgb ;// *ubo.Ambient.rgb * specDotR;
		//spColor = ComputeTexMulFactor(spColor, ubo.SphereTexMulFactor);
		//spColor = ComputeTexAddFactor(spColor, ubo.SphereTexAddFactor);
		if (SphereTexMode == 1)
		{
			color *= spColor;
		}
		else if (SphereTexMode == 2)
		{
			color += spColor;
		}
	}

 #endif


	//color=color*color*1.2;

	outFragColor = vec4(color, alpha);
	//outFragColor=vec4(1,1,0,1);
}
