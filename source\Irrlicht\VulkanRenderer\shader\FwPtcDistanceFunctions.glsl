/// Mask Functions

float roundDf(float dist)
{
	return smoothstep(0,0.05,-dist);
}

float fillDf(float dist)
{
	return smoothstep(0,0.05,-dist);
}


float borderDf(float dist, float width)
{

	return 1.0-smoothstep(0,0.05,(abs(dist)-width));;
}



/// Distance Functions
float dfCircle(in vec2 p, in float r)
{
    return length(p)-r;
}

float dfStar(in vec2 p, in float r, in float n, in float m)
{
    // next 4 lines can be precomputed for a given shape
    float an = PI/(n);
    float en = PI*2/m;
    vec2  acs = vec2(cos(an),sin(an));
    vec2  ecs = vec2(cos(en),sin(en)); // ecs=vec2(0,1) for regular polygon,

    float bn = mod(atan(p.x,p.y)
	//+fv.x*(fv.w-0.5)*(2*PI)
	//+(fv.w-0.5)*(2*PI)
	,2.0*an) - an;
    p = length(p)*vec2(cos(bn),abs(sin(bn)));
    p -= r*acs;
    p += ecs*clamp( -dot(p,ecs), 0.0, r*acs.y/ecs.y);
    return length(p)*sign(p.x);
}

float df6Side( in vec2 p, in float r )
{
	const vec3 k = vec3(-0.866025404,0.5,0.577350269);
	p = abs(p);
	p -= 2.0*min(dot(k.xy,p),0.0)*k.xy;
	p -= vec2(clamp(p.x, -k.z*r, k.z*r), r);
	return length(p)*sign(p.y);
}

float function( float r, float t ) {
	//return 1.0 - 0 *cos( 6.0 * t ) - cos(5*(15+1*fv)/ 16.0 * t  ) - r;
	//return 1.0 - 1 *cos( 3.0 * t ) - cos( 15.0*(7+2*fv)/ 16.0 * t  ) - r;
	//return 2.0 - cos( 3.0 * t ) - cos( 15.0 * 3.0 * t / 16.0 ) - r;
	//return 2.0 - cos( 3.0 * t ) - cos( 15.0 * 3.0 * t / 16.0 ) - sin( r );
	return 2.0 - cos( 6.0 * t ) - cos( 15.0 * 3.0 * t / 16.0 ) - cos( r * 4.0 );
}

float solve( vec2 p ) {


	float r = length( p );
	float t = atan( p.y, p.x ) ;
	
	float v = 1000.0;
	for ( int i = 0; i < 16; i++ ) {

		v = min( v, abs( function( r, t ) ) );
		t += PI * 2.0;
	}
	return v;
}

float value( vec2 p, float size ) {
	return 1.0 / max( solve( p ) / size, 1.0 );
}