#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;


layout (binding = 2)  uniform sampler2D g_tex0_sampler;




	layout(location = 0) in vec4 i_colorD;
	layout(location = 1) in vec2 i_tex0;




//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;

// adding pixel shader
void main()
{
	vec4 c =  texture(g_tex0_sampler,i_tex0) * i_colorD; 
	outFragColor = vec4(c.rgb, dot(c.rgb, vec3( 0.3 , 0.59 , 0.11 )));
}