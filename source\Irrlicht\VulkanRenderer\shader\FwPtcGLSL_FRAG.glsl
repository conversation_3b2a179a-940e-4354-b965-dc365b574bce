
#include "GsParticleShared.h"

#include "FwParticleShaderShared.glsl"
#include "FwPtcDistanceFunctions.glsl"
#define CALC_TEX 1

layout(location = 0) in	vec4 color;
layout(location = 1) in	vec4 tex;// all interpolation
layout(location = 2) in	vec4 fv;// all interpolation

layout (binding = 3) uniform sampler2DArray samplerArray;

#if FW_USE_SHADOWMAP
layout(location = 3) in	vec4 inPos; 
layout (binding = 4) uniform sampler2D SphereTex;
#define ambient 0.1
float textureProj(vec4 shadowCoord, vec2 off)
{
	float shadow = 1.0;
	if ( shadowCoord.z > -1.0 && shadowCoord.z < 1.0 ) 
	{
		float dist = texture( SphereTex, (vec2(1,-1)*shadowCoord.st+1)/2 + off ).r;
		if ( shadowCoord.w > 0.0 && dist < shadowCoord.z*0.9999995f )
		{
			shadow = ambient;
		}
	}
	return shadow;
}
float filterPCF(vec4 sc)
{

	#if 0
	return  textureProj(sc, vec2(0,0));
	#else
	ivec2 texDim = textureSize(SphereTex, 0);
	const float scale = 0.5;
	float dx = scale / float(texDim.x);
	float dy = scale / float(texDim.y);
	float shadowFactor = 0.0;
	int count = 0;
	const int range = 1;	
	for (int x = -range; x <= range; x++)	for (int y = -range; y <= range; y++)	{
			shadowFactor += textureProj(sc, vec2(dx*x, dy*y));
			count++;
	}		
	return shadowFactor / count;
	#endif
}
#endif

#if !FW_OIT

layout(location = 0) out vec4 outFragColor;

#elif FW_OIT == 1

// LinkedList OIT (UseOIT=1)
#include "oit_utils.h"

layout (early_fragment_tests) in;

layout(binding = 7, r32ui) uniform highp uimage2D counterImage;
layout (std430,binding = 8) buffer CountBuffer 
{
	uint oit_counter;
	uint maxCount;  // OIT_LAYERS for interlock
	float minAlpha,pad;
	uint viewW;
	uint viewH;
	uint viewSize;
	uint pad2;
};

layout (std430, binding = 10) writeonly buffer oitData {
	OITData data[];
};

#elif FW_OIT == 2

// Interlock OIT (UseOIT=2)
#include "oit_utils.h"

// Check if interlock is supported
#if defined(GL_NV_fragment_shader_interlock) || defined(GL_ARB_fragment_shader_interlock)
#define INTERLOCK_SUPPORTED 1
#else
#define INTERLOCK_SUPPORTED 0
#endif
layout (std430,binding = 8) buffer CountBuffer 
{
	uint oit_counter;
	uint maxCount;  // OIT_LAYERS for interlock
	float minAlpha,pad;
	uint viewW;
	uint viewH;
	uint viewSize;
	uint pad2;
};
#if INTERLOCK_SUPPORTED
// Enable interlock extensions
#ifdef GL_NV_fragment_shader_interlock
#extension GL_NV_fragment_shader_interlock : enable
layout(pixel_interlock_ordered) in;
#define beginInvocationInterlock beginInvocationInterlockNV
#define endInvocationInterlock endInvocationInterlockNV
#elif defined(GL_ARB_fragment_shader_interlock)
#extension GL_ARB_fragment_shader_interlock : enable
layout(pixel_interlock_ordered) in;
#define beginInvocationInterlock beginInvocationInterlockARB
#define endInvocationInterlock endInvocationInterlockARB
#endif



// A-buffer as buffer instead of imageBuffer
layout (std430,binding = 10) buffer OITAbuffer 
{
	uvec2 abuffer_data[];
};

layout(binding = 11, r32ui) uniform coherent uimage2D auxImage;
layout(binding = 12, r32ui) uniform coherent uimage2D auxDepthImage;

#else
// Fallback to LinkedList if interlock not supported
layout (early_fragment_tests) in;

layout(binding = 7, r32ui) uniform highp uimage2D counterImage;
 
layout (std430, binding = 10) writeonly buffer oitData {
	OITData data[];
};
#endif

#endif


void main() 
{
#if FW_OIT
	 vec4 outFragColor;
#endif

#if CALC_TEX 
#if FW_USE_TEXARRAY
	if (tex.z<250)
	{
		vec4 c = texture(samplerArray, tex.xyz);
		outFragColor = 
		color*vec4(1,1,1,c.r) + c.a*color.a;//  		
		//color*c.r + c.a*color.a;//  		
		
		//vec4(c.rgb,tex.a*c.r)*color + c.a*color.a;
	}
	else
#endif
	{
		
		//vec4 cc = texture(samplerArray, vec3(tex.xy,tex.z-256));		outFragColor = vec4(cc.rgb,tex.a*cc.r)*color + cc.a*color.a;return;
		vec3 rgb=vec3(1,1,1);
		float c,ca=0.0;
		int id= int(tex.z-255.0+0.5);
		vec2 pos=tex.xy*2.0-vec2(1.0);
#if 0
	c=0.5;
#else

		switch(id)
		{
			case 1:
			default://case 1 = 256
			{
				float r=length(pos);
				c=cos(r*PI/2);//*gl_FragCoord.z;
			}
			break;
			case 0://255
			{
				//c=1;
				float r=length(pos);
				c=smoothstep(0.0,0.1,1-r);
			}
			break;
			case 11://266
			{
				float r=clamp(1-length(pos),0,1); 
				c=r*r;
	
			}	
			break;
			case 12://267
			{

				c=clamp(exp(-12*(abs(length(pos))-0.1)),0,1); 	
				//c=max(exp(-12*(abs(length(pos))-0.2)),0); 		
				rgb=vec3(0,0,0);
			}	
			break;
			case 2://*
			{				
				float r=length(pos); 
				float a=atan(pos.y,pos.x);//+fv.x*fv.z;
				float f=abs(cos(a*(8/2.0)))*.5+.5;//(0.5+0.5*sin(r*PI*8)*(pow(abs(cos(a*5)),8)));
				
				if (r>f) discard;

				c=  1.0 -
				//step(f,r)
				smoothstep(f-0.95,f,r)
				 //f
				 ;

				//c *= 0.5 + 0.5*cos(r*20);
						ca= smoothstep(0.0,1,(sin(PI*32*c+fv.x*5)-0.5 )*c);
				//ca=  f;
				//((1.0 - smoothstep(0.0,0.27,r))) ;
			}
			break;
			
			case 3://*
			{
				
				float r=length(pos); 
				float a=atan(pos.y,pos.x)+fv.x*fv.w;
				float f=(
				abs(fract(5*(a/PI2))-0.5)*2
				)
				*0.5*fv.x
				+0.5
				;
				
				//if (r>f) discard;
				c=  1.0 -
				smoothstep(0,f,r)
				// f
				 ;


				//c=sin(c*PI2);
				//c *= 0.5 + 0.5*cos(r*20);
				//	ca= smoothstep(0.0,1,sin(PI*2*fv.x)*c);
				ca= smoothstep(0.0,1,sin(PI*6*c+fv.x*PI2)*c*0.5);
			}
			break;
			case 4://*
			{
				
				float r=length(pos); 
				float a=atan(pos.y,pos.x)+fv.w;
				float f=(
				abs(fract(5*(a/PI2))-0.5)*2
				)
				*0.5
				+0.5
				;
				
				//if (r>f) discard;
				c=  1.0 -
				smoothstep(0,f,r)
				// f
				 ;


				//c=sin(c*PI2);
				//c *= 0.5 + 0.5*cos(r*20);
				//	ca= smoothstep(0.0,1,sin(PI*2*fv.x)*c);
ca= smoothstep(0.0,1,sin(PI*6*c+fv.x*PI2)*c*0.5);
			}
			break;
			case 5://flower
			{

				//float r=length(pos)*2.0;

				float d=(dfStar(pos,0.5,5,4+  200 *(5*2.0-4)));
				d=d - (0.5-0.02);
				c = (1.0) - sign(d)*(0.5);
				c *= 1.05 - exp(2*(d));
				c *= 0.6 + 0.3*cos(PI*10.0*d);
				//c= mix( c, (1.0), 1.0-smoothstep(0.0,0.02,abs(d)) );

			}
			break;	
						break;
			// case 6:
			// {


			// 	float d=(dfStar(pos,0.5,5,4+1*(5*2.0-4)));
			// 	d=d - (0.5*fv.x-0.02);
			// 	c = (1.0) - sign(d);
			// 	c *= 1.05 - exp(2*(d));
			// 	//c *= 0.6 + 0.3*cos(PI*1.0*d);
				
			// }
			// break;
			case 8://heart
			{
				vec2 p=vec2(pos.x,-pos.y);
				p *=0.52;
				p.y= 0.057 + p.y*1.25 - abs(p.x) * (1.0-abs(p.x));
				float r=length(p);

				c= smoothstep(0,0.3, (0.5-0.02-r)); 
				ca= smoothstep(0.0,.5,sin(PI*12*r+fv.y*PI2)*c*.2);
				
			}
			break;		
			// case 17://   6 sides
			// {

			// 	float d=df6Side(pos,0.5);

			// 	//d=d - (0.5*fv.x);
			// 	c = 1.0- smoothstep(0,fv.x*.5,d);
			// 	//c = borderDf((d), 0.5*fv.x);
			// 	//c= mix( 0, (1.0), 1.0-smoothstep(0.0,0.2,abs(d)) );
			// }
			// break;

			break;


		}
#endif 

#if FW_OIT
			outFragColor =   

			vec4(1,1,1,c)	
		
			*color //+ ca*color.a
			;
#else	
				//if (c<0.01) discard;//discard may not improve performance
				outFragColor =  
				// vec4(c,c,c,c)*color
				vec4(rgb,max(0.0,c))*color
				// vec4(1,1,1,clamp(c,0,1))*color 
				+ ca*color.a;
#endif

	}

//outFragColor = vec4(c.rgb,c.r)*color + c.a*color.a;
	//	outFragColor=vec4(gl_FragCoord.z);
#else
		vec4 c = texture(samplerArray, tex.xyz);
		outFragColor = vec4(c.rgb,tex.a*c.r)*color + c.a*color.a;

#endif

#if FW_USE_SHADOWMAP
	if (inPos.w!=0.0)
	{
	vec4 suv= vec4(inPos.xyz,1)* mLightV; 
	if (suv.w>0.0) {
		float shadow=1.f;
		shadow=	filterPCF(suv / suv.w);

#if USE_LIGHT_TEX
			*(0.2f+0.8*texture(LightTex, 2*(vec2(1,-1)*suv.st/suv.w+1)/2).r)
#endif
		;
		outFragColor.rgb *=  0.5f+0.5f*shadow ;
	}
	}
#endif


#if FW_OIT == 1

// LinkedList OIT implementation
if (outFragColor.a<minAlpha ||oit_counter>OIT_BufSize-10240) {
		discard;
		return;
}

	uint idx = atomicAdd(oit_counter,1) ;

	if (idx < OIT_BufSize) {

		ivec2 coord = ivec2(gl_FragCoord.xy);
		uint prev = imageAtomicExchange(counterImage, coord, idx);

#if !PARAM_OIT
		outFragColor.rgb*= //(tex.w*outFragColor.a+1-tex.w)
	    	outFragColor.a//*tex.w
#if !FW_FOR_HDR10
		 	*colorMul   //HDR video will overflow, only for 2-pass mode
#endif
			 ;
		 	//  outFragColor.rgb=vec3(1,1,1);//*outFragColor.a;
			//   outFragColor.a=1;
#endif
	#if USE_UINT_COLOR
		uvec4 colorTemp = uvec4(outFragColor * 255.0);
		//uint alpha = uint(0.75 * 255);
		uint color =  (colorTemp.a << 24 )
		// 0xFF000000
				|(colorTemp.b << 16) | (colorTemp.g << 8) | colorTemp.r;		
		data[idx].color = color;
	#else
		data[idx].color = outFragColor;
	#endif
		data[idx].depth = gl_FragCoord.z;
		data[idx].prev = prev;
	}
discard;

#elif FW_OIT == 2

// Interlock OIT implementation
#if INTERLOCK_SUPPORTED
// Early alpha test to avoid unnecessary work
if (outFragColor.a < minAlpha) {
	discard; 
}

#if !PARAM_OIT
	outFragColor.rgb *= outFragColor.a;
#if !FW_FOR_HDR10
	outFragColor.rgb *= colorMul;
#endif
#endif

// Get viewport size dynamically from fragment coord and maxCount
// This is a workaround until proper viewport uniforms are added
const int OIT_LAYERS = int(maxCount);
ivec2 coord = ivec2(gl_FragCoord.xy);

// Compute index in the A-buffer using screen coord and a reasonable buffer size
// We'll use maxCount as both layer count and a size hint
const int sampleID = 0; // For non-MSAA
uint listPos = viewSize * sampleID + (coord.y * (viewW)  + coord.x); // Use standard 1920 width for now

// Pack color and depth for storage with better precision
uvec2 storeValue = uvec2(
	packUnorm4x8(outFragColor), // packed color (already premultiplied)
	floatBitsToUint(gl_FragCoord.z) // depth as uint - maintains full precision
);

// Critical section start
beginInvocationInterlock();

// Early depth test with improved logic
uint oldDepth = imageLoad(auxDepthImage, coord).r;
bool shouldProcess = (oldDepth == 0) || (storeValue.y <= oldDepth);

if (shouldProcess) {
	const uint oldCounter = imageLoad(auxImage, coord).r;
	imageStore(auxImage, coord, uvec4(oldCounter + 1));
	
	if (oldCounter < OIT_LAYERS) {
		// Insert fragment directly into A-buffer
		uint insertIndex = listPos + int(oldCounter) * viewSize;
		if (insertIndex >= 0 && insertIndex < abuffer_data.length()) {
			abuffer_data[insertIndex] = storeValue;
			// Update closest depth for early rejection
			if (oldDepth == 0 || storeValue.y < oldDepth) {
				imageStore(auxDepthImage, coord, uvec4(storeValue.y));
			}
		}
		// Fragment stored, clear output color for tail blending
		outFragColor = vec4(0);
	} else {
		// A-buffer is full, find the furthest fragment to replace
		int furthest = 0;
		uint maxDepthFound = 0;
		
		// Find the fragment with the largest depth (furthest from camera)
		for (int i = 0; i < OIT_LAYERS; i++) {
			uint bufferIdx = listPos + i * viewSize;
			if (bufferIdx >= 0 && bufferIdx < abuffer_data.length()) {
				const uint testDepth = abuffer_data[bufferIdx].y;
				if (testDepth > maxDepthFound) {
					maxDepthFound = testDepth;
					furthest = i;
				}
			}
		}
		
		// Only replace if our fragment is closer than the furthest stored fragment
		if (storeValue.y < maxDepthFound) {
			// Replace the furthest fragment and tail-blend the replaced one
			uint replaceIdx = listPos + furthest * viewSize;
			if (replaceIdx >= 0 && replaceIdx < abuffer_data.length()) {
				uvec2 replaced = abuffer_data[replaceIdx];
				outFragColor = unpackUnorm4x8(replaced.x); // Get replaced fragment for tail blending
				abuffer_data[replaceIdx] = storeValue;
				
				// Update depth buffer to reflect new closest depth
				uint newClosestDepth = storeValue.y;
				for (int i = 0; i < OIT_LAYERS; i++) {
					uint checkIdx = listPos + i * viewSize;
					if (checkIdx >= 0 && checkIdx < abuffer_data.length()) {
						uint checkDepth = abuffer_data[checkIdx].y;
						if (checkDepth < newClosestDepth) {
							newClosestDepth = checkDepth;
						}
					}
				}
				imageStore(auxDepthImage, coord, uvec4(newClosestDepth));
			}
		}
		// If our fragment is further than the furthest stored, just tail-blend it
	}
}
// else: fragment failed early depth test, just tail-blend it

endInvocationInterlock();
// Critical section end

// For interlock, we output the tail-blended color or zero
// The resolve pass will composite the stored fragments

#else
// Fallback to LinkedList implementation when interlock not supported
if (outFragColor.a<minAlpha ||oit_counter>OIT_BufSize-10240) {
		discard;
		return;
}

	uint idx = atomicAdd(oit_counter,1) ;

	if (idx < OIT_BufSize) {

		ivec2 coord = ivec2(gl_FragCoord.xy);
		uint prev = imageAtomicExchange(counterImage, coord, idx);

#if !PARAM_OIT
		outFragColor.rgb*= outFragColor.a;
#if !FW_FOR_HDR10
		 	outFragColor.rgb *= colorMul;
#endif
#endif
	#if USE_UINT_COLOR
		uvec4 colorTemp = uvec4(outFragColor * 255.0);
		uint color =  (colorTemp.a << 24 )
				|(colorTemp.b << 16) | (colorTemp.g << 8) | colorTemp.r;		
		data[idx].color = color;
	#else
		data[idx].color = outFragColor;
	#endif
		data[idx].depth = gl_FragCoord.z;
		data[idx].prev = prev;
	}
discard;
#endif

#endif
}


