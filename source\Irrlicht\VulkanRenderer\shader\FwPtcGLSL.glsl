
#define SM5UP 1

#define FW_MAX_TYPE_COUNT 256   //max 4096 vectors(32bit*4) : 4096*4*4/(80) =819   /96=682   /128=512
#define FW_GEN_MAX 256
#define PT_BANDS 32
#define GMODE_GenZonPR		0x1000
#define GMODE_GenTonPrT		0x2000		// gen t on parent t
#define GMODE_RotOnY		0x8000		// rotate on vec Y direction

#define CFLAG_TexRtoA		0x00000001u 
#define CFLAG_SpcOnZ		0x00010000u //ofs z
#define CFLAG_ImgTex		0x00220000u	// size on r
#define CFLAG_Vec0OnZ		0x00040000u	// v on z



//#define TFLAG_ColMask		0x000000FF	// 
#define TFLAG_ColMaskInv	0x00000100u
#define TFLAG_ColMaskRandom	0x00000200u
#define TFLAG_ColMaskIds	0x000000FFu
#define TFLAG_ColHueMask	0x0000000Fu	
#define TFLAG_ColHue		0x00000001u	
#define TFLAG_ColHueSin		0x00000002u	
#define TFLAG_ColSat		0x00000003u	
#define TFLAG_ColSatSin		0x00000004u	
#define TFLAG_ColLight		0x00000005u
#define TFLAG_ColLightSin	0x00000006u
#define TFLAG_ColToN		0x00000010u	// morphcolor to next

//#define TFLAG_OnBandv			// color on band v
#define TFLAG_OnBandV_Mask	0xFFFF0000u
#define TFLAG_OnBandV_A		0xF0000000u
#define TFLAG_OnBandV_H		0x0F000000u
#define TFLAG_OnBandV_S		0x00F00000u
#define TFLAG_OnBandV_L		0x000F0000u

#define CFLAG_ImgScatter	0x01000000u
#define CFLAG_ImgGather		0x02000000u
#define CFLAG_PtMovTo		0x10000000u	//Col is target xyz
#define CFLAG_PtMovAt		0x20000000u	//Col is target xyz

#define MFLAG_LandOnXMin	0x00000001u
#define MFLAG_LandOnXMax	0x00000002u
#define MFLAG_LandOnYMin	0x00000010u
#define MFLAG_LandOnYMax	0x00000020u
#define MFLAG_LandOnZMin	0x00000100u
#define MFLAG_LandOnZMax	0x00000200u


//Fire Work GS
//#define MAX_TRANS_COUNT 128	//must 4 x n  

#define PI 3.1415927


struct Ember {
	int emc;		//EmbCount	n>=1000: per ms  ,  n<1000: real count
	int	reb;		//Rebirth
	int	tt;
	int tt1;
	float life;	//life	
	float life1;	//second life	
	float spc;	// speed base
	float spr;	// speed Power
	float r;			//radius
	float m;   //m x g 
	float decml;
	int mpr;			//turning type idx
	int mpc;
	int	texId;
	int tex2;
	uint tflag, cflag, gmode;
	int tt2;
	uint mflag;
	vec4 fCol, fCol2, pad1;
};

struct GenFirework {
	int embId;
	int notused;
	int mode, bandId;
	vec3 pos; float pmz;
	vec3 vec; float padv1;
	vec4 col;
	vec3 tcl; float padv2;
};

layout( binding = 0) readonly uniform  GsFireWorkBuffer //frame 
{
	mat4 mVP;
 	//mat4 mVP;
 	mat4 mW;
 	mat4 mV;
 	mat4 mP;
 	mat4 mRotZ;
 	float g_fGlobalTime;
 	float g_fElapsedTime;
 	float randFac;
 	int kldCount;

	vec3 g_vFrameGravity;
	float g_fMaxEmber2s;
	vec3	launchPos;
	float fFrameDecelerate;
	

	float maxBandV;
	float fireQtyNotUsed;
	float firePow;
	float fireScale;

	vec3 eyePos;
	int GenCount;

	vec3 pointerPos;
	float pad01;
	int txtMin, txtMax, txtCvt, pad02;
	vec2 pad03;
	float CamClipZMin;
	float mainPeakValue;
	
	vec4 landMin;
	vec4 landMax;
	vec4 bandv[PT_BANDS];
	GenFirework gf[FW_GEN_MAX];

} ;

layout(binding = 1) readonly uniform  cbOnlyOnce //once
{
	Ember Emb[FW_MAX_TYPE_COUNT];
};

float CalcRatio(float ratio, uint mp)
{
	float ret;
	switch (mp & 0xfu)
	{
	case 1: ret = ratio;				break;
	case 2: ret = 1.0f - ratio;		break;
	case 3: ret = 0.5f + 0.5f*ratio;	break;
	case 4: ret = 1.0f - 0.5f*ratio;	break;
	default: ret = 1.0f;				break;
	}

	//if ((mp & 0xf0) == 0x10)		ret = sin(ret*PI / 2);
		switch (mp & 0xf0)
		{
			case 0x10:
			ret = sin(ret * PI / 2);
			break;
			case 0x20:
			ret = sin(ret * PI);
			break;
		}
	return ret;
}