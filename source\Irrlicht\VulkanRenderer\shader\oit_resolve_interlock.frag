#version 460

precision highp float;

#extension GL_GOOGLE_include_directive : enable
#include "oit_utils.h"
#extension GL_ARB_explicit_attrib_location : require
#extension GL_ARB_shader_image_load_store : require
#extension GL_ARB_shader_storage_buffer_object : require

layout (early_fragment_tests) in;

// Interlock OIT bindings
layout (std430,binding = 8) buffer CountBuffer 
{
	uint oit_counter;
	uint maxCount;
	float minAlpha, pad2;
};

// A-buffer as buffer 
layout (std430,binding = 10) buffer OITAbuffer 
{
	uvec4 abuffer_data[];
};

layout(binding = 11, r32ui) uniform coherent uimage2D auxImage;
layout(binding = 12, r32ui) uniform coherent uimage2D auxDepthImage;

layout(location = 0) out vec4 colorOut;

// Bubble sort for depth sorting - match NVIDIA sample exactly
void bubbleSort(inout uvec2 array[16], int n) {
	for(int i = (n - 2); i >= 0; --i) {
		for(int j = 0; j <= i; ++j) {
			if(uintBitsToFloat(array[j].g) >= uintBitsToFloat(array[j + 1].g)) {
				// Swap array[j] and array[j+1]
				uvec2 temp = array[j + 1];
				array[j + 1] = array[j];
				array[j] = temp;
			}
		}
	}
}

// Blending functions from NVIDIA sample
void doBlend(inout vec4 color, vec4 baseColor) {
	color.rgb += (1.0 - color.a) * baseColor.rgb;
	color.a += (1.0 - color.a) * baseColor.a;
}

void doBlendPacked(inout vec4 color, uint fragment) {
	vec4 unpackedColor = unpackUnorm4x8(fragment);
	// Assume already in correct color space for simplification
	unpackedColor.rgb *= unpackedColor.a; // Premultiply
	doBlend(color, unpackedColor);
}

float transferHDR(float e)
{
	const float a=1.0993,b=0.0181;
	if (e>b) return a*pow(e,0.45)-(a-1);
	else return e*0.45;
}

void main(void)
{
	ivec2 coord = ivec2(gl_FragCoord.xy);
	
	// Match NVIDIA sample structure exactly
	const int OIT_LAYERS = int(clamp(maxCount, 1u, 16u));
	const int viewSize = 1920 * 1080; // Should match viewport size from NVIDIA sample
	const int sampleID = 0; // For MSAA support
	const int listPos = viewSize * OIT_LAYERS * sampleID + (coord.y * 1920 + coord.x);
	
	// Get number of fragments for this pixel
	uint fragments = imageLoad(auxImage, coord).r;
	fragments = min(uint(OIT_LAYERS), fragments);
	
	if (fragments == 0) {
		colorOut = vec4(0);
		return;
	}
	
	// Load fragments from A-buffer - use uvec2 to match NVIDIA sample
	uvec2 fragmentArray[16]; // Support up to 16 layers
	int actualFragments = int(min(fragments, 16u));
	
	for (int i = 0; i < actualFragments; i++) {
		int bufferIdx = listPos + i * viewSize;
		if (bufferIdx >= 0 && bufferIdx < abuffer_data.length()) {
			uvec4 fullFragment = abuffer_data[bufferIdx];
			// Extract color and depth only (uvec2)
			fragmentArray[i] = uvec2(fullFragment.r, fullFragment.g);
		}
	}
	
	// Sort fragments front-to-back by depth - match NVIDIA sample
	bubbleSort(fragmentArray, actualFragments);
	
	// Blend fragments front-to-back - match NVIDIA sample approach
	vec4 colorSum = vec4(0); // Initially completely transparent
	
	for(int i = 0; i < actualFragments; i++) {
		doBlendPacked(colorSum, fragmentArray[i].x);
	}
	
	colorOut = colorSum;
}
