#version 460
#extension GL_GOOGLE_include_directive : enable
#include "FFCodeHeader.glsl"
precision highp float;
// References HLSL
// Flow control: http://msdn.microsoft.com/en-us/library/bb509600(VS.85).aspx
// Semantics: http://msdn.microsoft.com/en-us/library/bb509647(VS.85).aspx

// Constants
#define USE_SPECULAR 0

#define MAX_LIGHTS 									2
#define EMT_LIGHTMAP								2
#define EMT_LIGHTMAP_ADD							3


layout (binding = 2)  uniform sampler2D g_tex0_sampler;





layout(location = 0) in vec2 i_tex0;
layout(location = 1) in vec3 i_wNorm;       //world space normal
layout(location = 2) in vec4 i_colorD;
#if USE_SPECULAR 
layout(location = 3) in vec4 i_colorS;
#endif
layout(location = 4) in vec4 i_wPos;       //world space normal
//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;

// adding pixel shader
void main()
{
	//if (i_wPos.y<clipY) discard;
	//return float4(1,1,0,1);
	vec4 normalColor;// = float4(0, 0, 0,0);
	vec4 tex1 =  texture(g_tex0_sampler,i_tex0); 
	normalColor = (tex1*i_colorD ) 
#if USE_SPECULAR 	
	//	+ i_colorS
#endif
		;
	if (tex1.a<0.001) discard;
	outFragColor = normalColor;
}