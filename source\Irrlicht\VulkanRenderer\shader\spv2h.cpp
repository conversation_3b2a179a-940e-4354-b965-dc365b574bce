#include <iostream>
#include <fstream>
#include <vector>
#include <iomanip>
#include <cstdint>
#include <string>

void spv_to_c_header(const std::string& spv_file_path, const std::string& output_header_path, const std::string& part1, const std::string& part2) {
    // Read SPIR-V binary file
    std::ifstream spv_file(spv_file_path, std::ios::binary | std::ios::ate);
    if (!spv_file.is_open()) {
        std::cerr << "Failed to open SPIR-V file: " << spv_file_path << std::endl;
        return;
    }

    std::streamsize file_size = spv_file.tellg();
    spv_file.seekg(0, std::ios::beg);

    std::vector<uint32_t> spv_data(file_size / sizeof(uint32_t));
    if (!spv_file.read(reinterpret_cast<char*>(spv_data.data()), file_size)) {
        std::cerr << "Failed to read SPIR-V file: " << spv_file_path << std::endl;
        return;
    }    spv_file.close();

    // Replace '/' and '\' with "__" in part1 for variable name
    std::string sanitized_part1 = part1;
    size_t pos = 0;
    while ((pos = sanitized_part1.find('/', pos)) != std::string::npos) {
        sanitized_part1.replace(pos, 1, "__");
        pos += 2;
    }
    pos = 0;
    while ((pos = sanitized_part1.find('\\', pos)) != std::string::npos) {
        sanitized_part1.replace(pos, 1, "__");
        pos += 2;
    }

    // Construct the variable name
    std::string array_name = "SpirV_" + sanitized_part1 + "_" + part2;

    // Open output header file
    std::ofstream header_file(output_header_path);
    if (!header_file.is_open()) {
        std::cerr << "Failed to open output header file: " << output_header_path << std::endl;
        return;
    }

    // Write the header content
    header_file << "#pragma once\n";
    header_file << "const uint32_t " << array_name << "[] = {\n";

    // Write the SPIR-V data in hex format, grouping by 8 per line
    for (size_t i = 0; i < spv_data.size(); i++) {
        if (i % 8 == 0) {
            header_file << "\t";
        }
        header_file << "0x" << std::setfill('0') << std::setw(8) << std::hex << spv_data[i];
        if (i != spv_data.size() - 1) {
            header_file << ",";
        }
        if (i % 8 == 7 || i == spv_data.size() - 1) {
            header_file << "\n";
        }
    }

    header_file << "};\n";
    header_file.close();

    std::cout << "Header file generated: " << output_header_path << std::endl;
}

int main(int argc, char* argv[]) {
    if (argc != 3) {
        std::cerr << "Usage: " << argv[0] << "   varname shadertype\n";
        return 1;
    }
 
    std::string part1 = argv[1];
    std::string part2 = argv[2];

    char spv_file_path[512], output_header_path[512];
    snprintf(spv_file_path,512,"Compiled\\%s_%s_SpirV.spv", argv[1], argv[2]);
    snprintf(output_header_path, 512, "Compiled\\%s_%s_SpirV.h", argv[1], argv[2]);


    spv_to_c_header(spv_file_path, output_header_path, part1, part2);

    return 0;
}
