
const vec3 gfavalue={-1,2,2}, fb={3,2,4},fk={1,-1,-1};

vec3 HUEtoRGB(in float H)
  {

     float R = abs(H * 6 - 3) - 1;
     float G = 2 - abs(H * 6 - 2);
     float B = 2 - abs(H * 6 - 4);
	return clamp(vec3(R,G,B),0.0,1.0);

  }



#define NO_ASM 1
vec3 RGBtoHSL(in vec3 RGB)
  {
    vec3 HSL=vec3(0.0,0.0,0.0);
    float U, V;
  #if NO_ASM
    U = -min(RGB.r, min(RGB.g, RGB.b));
    V = max(RGB.r, max(RGB.g, RGB.b));
  #else
    float4 RGB4 = RGB.rgbr;
    asm  { max4 U, -RGB4 };
    asm { max4 V, RGB4 };
  #endif
    HSL.z = (V - U) * 0.5;
    float C = V + U;
    if (C != 0)
    {
      vec3 Delta = (V - RGB) / C;
      Delta.rgb -= Delta.brg;
      Delta.rgb += vec3(2,4,6);
      // NOTE 1
      Delta.brg = step(V, RGB) * Delta.brg;
  #if NO_ASM
      HSL.x = max(Delta.r, max(Delta.g, Delta.b));
  #else
      float4 Delta4 = Delta.rgbr;
      asm { max4 HSL.x, Delta4 };
  #endif
      HSL.x = fract(HSL.x / 6);
      HSL.y = C / (1 - abs(2 * HSL.z - 1));
    }
    return HSL;
  }

 vec3 HSLtoRGB(in vec3 HSL)
  {
    vec3 RGB = //HUEtoRGB(HSL.x)
		clamp(gfavalue+fk* abs(HSL.x*6-fb),0.0,1.0)
		;
    float C = (1 - abs(2 * HSL.z - 1)) * HSL.y;
    return (RGB - 0.5) * C  + HSL.z;
  }


// vec3 beautifulColor(in float x)
//   {
//     vec3 A=vec3(0.5,0.5,0.5),
//     B=vec3  (0.5,0.5,0.5),
//     C =vec3 (1,1,1),
//     D  =vec3(0.,0.,0.);

//      return A+B*cos(2*3.1415927*(C*x+D));
//   }