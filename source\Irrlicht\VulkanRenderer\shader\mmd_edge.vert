#version 460
#extension GL_GOOGLE_include_directive : enable


#extension GL_ARB_separate_shader_objects : enable
#extension GL_ARB_shading_language_420pack : enable

#include "FFCodeHeader.glsl"

	layout(location = 0) in vec3 inPos;
	layout(location = 1) in vec3 inNor;
	layout(location = 2) in vec4 i_color;
	layout(location = 3) in vec2 inUV;



out gl_PerVertex 
{
    vec4 gl_Position;
};

void main() 
{
    mat4 wv=g_mWorld * g_mView ;
    vec3 nor =  inNor* mat3(wv) ;
    vec4 pos = vec4(inPos.xyz, 1.0) * wv * g_mProj ;
    vec2 screenNor = normalize(vec2(nor));
    pos.xy += screenNor * vec2(1.0) / (fvecs[0].xy *0.5) * fv * pos.w;
    pos.z+=0.1f;
    gl_Position = pos;
}
