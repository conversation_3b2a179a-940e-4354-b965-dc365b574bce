#version 460
#extension GL_GOOGLE_include_directive : enable


#extension GL_ARB_separate_shader_objects : enable
#extension GL_ARB_shading_language_420pack : enable

#include "FFCodeHeader.glsl"
#include "mmd_0_header.glsl"

layout(std430, binding = 7) readonly buffer  PosNormOut //
{ 
    PosNormS pno[]; 
};			
	

layout (location = 0) out vec3 outPos;
layout (location = 1) out vec3 outNor;
layout (location = 2) out vec3 outEyePos;
layout (location = 3) out vec2 outUV;
layout (location = 4) out vec3 outNorV;
layout (location = 5) out flat int outiv;
layout (location = 6) out vec4 vtxpm;
out gl_PerVertex 
{
    vec4 gl_Position;
};

void main() 
{
	PosNormS vtx=pno[gl_VertexIndex];
	mat4 wv=g_mWorld * g_mView ;
	gl_Position = vec4(vtx.pos.xyz, 1.0) * wv * g_mProj ;
	outPos = ( vec4(vtx.pos.xyz, 1.0) *g_mWorld).xyz;
	outNor = normalize(vtx.nor.xyz*mat3(g_mWorld)) ;
	outNorV = normalize(outNor*mat3(g_mView));
	outEyePos = g_eyePos;
	//outNorWV = normalize(inNor*mat3(wv)) ;
	outUV = vec2(vtx.u, vtx.v);
	outiv = gl_VertexIndex+1;
	vtxpm.xyz = vtx.dtPos;
	vtxpm.w = vtx.velLen  ;
		//	gl_Position.z = min(gl_Position.z, gl_Position.w); //clamp z to 0-1, eg  SKY box

}
