#version 460
#extension GL_GOOGLE_include_directive : enable


#extension GL_ARB_separate_shader_objects : enable
#extension GL_ARB_shading_language_420pack : enable

#include "FFCodeHeader.glsl"

	layout(location = 0) in vec3 inPos;
	layout(location = 1) in vec3 inNor;
	layout(location = 2) in vec4 i_color;
	layout(location = 3) in vec2 inUV;
	


layout (location = 0) out vec3 outPos;
layout (location = 1) out vec3 outNor;
layout (location = 2) out vec3 outEyePos;
layout (location = 3) out vec2 outUV;
layout (location = 4) out vec3 outNorV;
layout (location = 5) out flat int outiv;
layout (location = 6) out vec4 vtxpm;
out gl_PerVertex 
{
    vec4 gl_Position;
};

void main() 
{
	mat4 wv=g_mWorld * g_mView ;
	gl_Position = vec4(inPos.xyz, 1.0) * wv * g_mProj ;
	outPos = ( vec4(inPos.xyz, 1.0) *g_mWorld).xyz;
	outNor = normalize(inNor*mat3(g_mWorld)) ;
	outNorV = normalize(outNor*mat3(g_mView* g_mProj ));
	outEyePos = g_eyePos;
	//outNorWV = normalize(inNor*mat3(wv)) ;
	outUV = vec2(inUV.x, inUV.y);
	outiv = gl_VertexIndex+1;
	vtxpm = vec4(0);
}
