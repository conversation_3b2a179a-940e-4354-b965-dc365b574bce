#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable
#include "GsParticleShared.h"



layout (local_size_x = 1) in;
//#include "colorutil.glsl"
#define IS_CS 1
#include "FwParticleShaderShared.glsl"





void main() 
{
	//memoryBarrier();
	drawCmd = GlslVkDrawIndexedIndirectCommand(showCountOut*6,1, 0,0,0);
	dsipCmd = GlslVkDispatchIndirectCommand( uint( ceil( float(showCountOut)/float(CS_LOCAL_SIZE_X))),1,1);
	//dsipCmd = GlslVkDispatchIndirectCommand( uint(ceil(1.0 )),1,1);
	//showCount = showCountOut;
	atomicExchange(showCount,showCountOut);
	atomicExchange(showCountOut,0);
	totalCount=freeCount+showCount;
	freeB = freeB % MaxPtcCount;
	freeF = freeF % MaxPtcCount;
	memoryBarrier();//memoryBarrierBuffer();
}