#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable


#include "GsParticleShared.h"
const vec3 SixWay[6]={{1,0,0},{-1,0,0},{0,1,0},{0,-1,0},{0,0,1},{0,0,-1}};
//#define USE_MFLAG_LAND  1// KLD_MIRROR

#define HAS_TEXTURE 0

layout (local_size_x = CS_LOCAL_SIZE_X) in;
#include "colorutil.glsl"

#define IS_CS 
#include "FwParticleShaderShared.glsl"
#if FW_HAS_IMG_COLOR
//layout (binding = 4) uniform sampler2D samplerTex1;
#endif

float rand01(float seed, in vec2 uv)//0 ~ 1
{
	float result = fract(sin(seed * dot(uv, vec2(12.9797f, 78.733f)	)) * 27157.5253f);
	return result;
}

float rand(float seed, in vec2 uv)//-1 ~ 1
{
	float result = fract(sin(seed * dot(uv, vec2(12.9898f, 78.733f))) * 17157.0523f);
	//seed += 1.0f;
	return result*2-1;
}
vec3 RandomDir(float fOffset, vec2 uv,float fDiv)
{
	//float tCoord = fOffset;//(g_fGlobalTime + fOffset) / fDiv + randFac;
	return vec3(rand(fOffset,uv*0.198),rand(fOffset+17.251,uv*0.231),rand(fOffset+231.27912,uv*0.137));
}
// returns a random float in range (0, 1). seed must be >0!
#if 1
// Optimized implementation avoiding expensive trig
mat3 GetRotYMatFromVec(vec3 vel) {
    // Fast path for zero velocity
    if (dot(vel, vel) < 0.000001) {
        return mat3(1.0); // Identity matrix
    }
    
    vec3 vn = normalize(vel);
    
    // Avoid acos by working with the dot product directly
    float cosTheta = dot(vec3(0, 1, 0), vn);
    
    // Fast path for nearly vertical vectors
    if (abs(cosTheta) > 0.9999) {
        return mat3(sign(cosTheta)); // Identity or flipped
    }
    
    // Calculate sin(theta) from cos(theta) using the Pythagorean identity
    float sinTheta = sqrt(1.0 - cosTheta * cosTheta);
    
    // Calculate cross product directly
    vec3 axis = vec3(-vn.z, 0.0, vn.x);
    float invLen = 1.0 / length(axis);
    axis *= invLen;
    
    // Rodrigues rotation formula (more efficient than full matrix construction)
    float x = axis.x, y = axis.y, z = axis.z;
    float c = cosTheta;
    float s = sinTheta;
    float t = 1.0 - c;
    
    mat3 result;
    result[0][0] = t*x*x + c;    result[0][1] = t*x*y + s*z;  result[0][2] = t*x*z - s*y;
    result[1][0] = t*x*y - s*z;  result[1][1] = t*y*y + c;    result[1][2] = t*y*z + s*x;
    result[2][0] = t*x*z + s*y;  result[2][1] = t*y*z - s*x;  result[2][2] = t*z*z + c;
    
    return result;
}
#else
mat3 GetRotYMatFromVec(vec3 vel)
{
	mat3 mRV;
	vec3 vn = (length(vel) == 0.0) ? vec3(0, 1, 0) : normalize(vel);

	float rad = acos(dot(vec3(0, 1, 0), vn));
	float c = cos(rad), s = sin(rad), ac = 1.0 - c;
	mat3 mRotV;
	vec3 nc = (cross(vn,vec3(0, 1, 0)));
	  nc = (length(nc) ==0.0) ? vec3(0, 0,1) : 	normalize(nc);
	float x = nc.x, y = nc.y, z = nc.z;
	mRV[0][0] = c + ac*x*x;		mRV[0][1] = ac*x*y + s*z;	mRV[0][2] = ac*x*z - s*y; //m[0][3] = 0;
	mRV[1][0] = ac*x*y - s*z;	mRV[1][1] = c + ac*y*y;		mRV[1][2] = ac*y*z + s*x; //m[1][3] = 0;
	mRV[2][0] = ac*x*z + s*y;	mRV[2][1] = ac*y*z - s*x;	mRV[2][2] = c + ac*z*z; //m[2][3] = 0;
																					//m[3][0] = 0; m[3][1] = 0; m[3][2] = 0;								 m[3][3] = 1;

	return mRV;
}
#endif
vec3 getVecRotY(vec3 vel)
{
	mat3 mRV;
	vec3 vn = (length(vel) == 0.0f) ? vec3(0, 1, 0) : normalize(vel);

	float rad = acos(dot(vec3(0, 1, 0), vn));
	float c = cos(rad), s = sin(rad), ac = 1.f - c;
	mat3 mRotV;
	vec3 nc = (cross(vn,vec3(0, 1, 0)));
																	//m[3][0] = 0; m[3][1] = 0; m[3][2] = 0;								 m[3][3] = 1;

	return nc;
}

void GenParticle(VSParticle pt)
{
	int freeIdx= int(atomicAdd(freeCount,-1))-1;
	uint newShowIdx=atomicAdd(showCountOut,1);

	if (freeIdx<CS_LOCAL_SIZE_X*8 || newShowIdx>=MaxPtcCount)
	{
		atomicAdd(freeCount,1);
		atomicAdd(showCountOut,-1);
		memoryBarrier();
		return;
	}
	uint ffIdx=atomicAdd(freeB,1);
	uint pid= FreeIds[ffIdx % MaxPtcCount];

	Pts[pid]= pt;
	
	idsOut[newShowIdx]=pid;
	memoryBarrier();
}


#define PTC Pts[pid]	//VSParticle ptc= Pts[pid];


void OutParticle(uint pid)
{

	uint newShowIdx = atomicAdd(showCountOut,1);
	if (newShowIdx<MaxPtcCount)
		idsOut[newShowIdx] = pid;
	else
		atomicAdd(showCountOut,-1);
	memoryBarrier();
	return ;
}
void FreeParticle(uint pid)
{
#if 0
	uint toFreeIdx= atomicAdd(toFreeCount,1);
	if (toFreeIdx<MaxPtcCount)
		FreeIds[MaxPtcCount+toFreeIdx] = pid;
	else
		atomicAdd(toFreeCount,-1);
	memoryBarrier();
#else
	uint freeIdx=atomicAdd(freeCount,1);

	if (freeIdx<MaxPtcCount)
	{
		uint ffIdx=atomicAdd(freeF,1);

		FreeIds[ffIdx%MaxPtcCount] = pid;
	}
	else
		atomicAdd(freeCount,-1);
	memoryBarrier();
#endif
}


#define PTCBACK PTC=ptc



void generateEmber(in vec2 uv, in uint pid,in uint eid,in float origRatio ,  in VSParticle opt)
{

	opt.eid = eid;
	Ember emb = Emb[opt.eid];

#if FW_VTX_MATS
	if (emb.mid==0 && opt.mid!=0)
	{
		VtxMatStruct vm=vtxMats[opt.mid]; 
		mat4  m=vm.m; 
		opt.pos = (vec4(opt.pos,1) * m).xyz;	
		
		vec3 dir=opt.pos-vm.v1;  			 
		float vmul= vm.v0p/length(dir)+1;//pow(vm.v0p/length(dir),2)+1;
		if (vm.v1type==1) {opt.vel=vm.v0.xyz+ normalize(dir)*vmul*vm.v1p; }
		else opt.vel   = opt.vel * mat3(m)+ vm.v0.xyz;
		opt.mid=0;//emb.mid;		
	}
#endif

	uint count = min(1024, emb.emc);  
	vec3 curVec = opt.vel;
	vec3 curPos=opt.pos;
									//curVec=mul( curVec, (vec3x3)mWV );
									//curVecN = normalize(mul( curVec,(vec3x3)mRotZ));

//	opt.ofs.z = ptc.ofs.z;

	// if (bool(Emb[ptc.eid].cflag & (CFLAG_ImgGather | MF_PtMovTo)) ) 
	//     opt.pos = ptc.tcl; //fix child position
	// else
		//opt.pos = ptc.pos;//+ (ptc.vel*g_fElapsedTime);
	opt.reb = emb.reb;
	vec3 vRandom = normalize(RandomDir(float(count) + randFac +  opt.ofs.x,uv, 11.31f));
	//vRandom = clamp(vRandom,-1,1);
#if FW_TO_MIDI
	//sound 
	{
				uint sid= emb.soundId;
				uint key= (sid & 0xFF);  //128~134 = channel 10 ~16
				if (key<128)
					key =  //uint(clamp(opt.pos.y/5,0,127)); // 
					uint( clamp(key+ emb.soundAdd*vRandom.z,0,127));
				if (key<136 ) {
					vec3 toEye=opt.pos-eyePos;
					float toEyeDis=length(toEye);
					soundCounts[key]+= emb.soundVel* clamp(1000/toEyeDis,0,10) ;//atomicAdd(soundCounts[sid],1);  MMD_SABA_SCALE * 10
				}
	}
#endif 
mat3 mRV= mat3(1);
	if (bool(emb.gmode & GMODE_RotOnY)) mRV = GetRotYMatFromVec(curVec);

	uint gm=emb.gmode;

	float spc = emb.spc *firePow* (bool(emb.cflag & CFLAG_VelonRatio) ? (origRatio): 1.f)
	*(bool( emb.cflag & CFLAG_SpcOnZ)?opt.ofs.z: 1.0) ;
	if (bool(gm&GMODE_WITH_SPR2)) spc+= emb.fv1.x*dot(curVec,curVec) ;

#if !FW_HIGH_PERFORMANCE
 	 if (bool(emb.cflag & 0xF)) {
	 
		if (bool(emb.cflag & CFLAG_ChgCol))
		{	if (bool(emb.cflag & CFLAG_ChgCol_OnlyA)) opt.col.a=emb.fCol.a; else
			opt.col=emb.fCol;
		}
		if (bool(emb.cflag & CFLAG_ChgHSLAMM)) {
			vec3 hsl = (RGBtoHSL(opt.col.rgb)); 
        	hsl = vec3(fract(hsl.r+emb.fCol2.r+vRandom.x*emb.fCol2.a),hsl.g*emb.fCol2.g,hsl.b*emb.fCol2.b);
			opt.col.rgb = HSLtoRGB(hsl);
	 	}
		else if (bool(emb.cflag & CFLAG_ChgColOnRatio)){
		 		vec3 hsl = (RGBtoHSL(opt.col.rgb)); 
        hsl.b= (1-origRatio)*hsl.b;
		opt.col.rgb= HSLtoRGB(hsl);
		 }	 
	  }
	
#endif	

#if FW_HAS_IMG_COLOR                
		//	if (bool(emb.gmode & GMODE_ImgCol))
		//		opt.col= texture(samplerTex1, vec2(opt.tcl.x/1920.0+0.5,0.5-opt.tcl.y/1080.0));//  
#endif


	for (uint i = 0; i<count; i++)
	{
		//opt.pos= ptc.pos+ptc.vel*g_fElapsedTime*i/count;
		opt.ofs.x = fract(opt.ofs.x + vRandom.x);// fmod(opt.ofs.x + vRandom.x, 1.f);
		float iratio = float(i) / count;

		vec3 vRand=RandomDir(iratio +  randFac + opt.ofs.x + opt.ofs.y,vec2(g_fGlobalTime,randFac*17), 17.31f);	
		float vRandLength = length(vRand);
		vRand = (vRandLength == 0.0) ? vec3(0.0, 1.0, 0.0) : vRand / vRandLength;//normalize() need check length too!
		vRand = clamp(vRand,-1,1);
		
		{

			float ik = (i + ((PTC.reb - 1)*0.5)) / count;

			switch (gm & GMODE_DirMask)
			{
			case 0:
			{
				opt.vel = (curVec*emb.spr + vRand*(spc
				+ i*randFac //todo check need
				));
			}
			break;
 		
			// case 0x2: //rot y ring xz
			// {
			// 	float a = -PI + 2 * PI*(ik);
			// 	opt.vel = (bool(gm & 0x8000) ? (vec3(sin(a), 0, cos(a))* mRV) : vec3(sin(a), 0, cos(a))) * spc + curVec*emb.spr;
			// }
			// break;
			// case 0x3: //rot z ring xy
			// {
			// 	float a = -PI + 2 * PI*(ik);
			// 	opt.vel = vec3(sin(a), cos(a), 0)* spc + curVec*emb.spr;
			// }
			// break;	
			case 0x10: //ring xz 1
			{
				float a = -PI + 2 * PI*(ik);
				opt.vel = vec3(sin(a), 0, cos(a)) *(spc )+ ((gm&GMODE_WITH_SPR)>>8)*curVec*emb.spr;
				
			}
			break;		
			case 0x11: //ring xy 1
			{
				float a = 2 * PI*(ik);
				opt.vel = vec3(cos(a), sin(a), 0) *(spc )+ ((gm&GMODE_WITH_SPR)>>8)*curVec*emb.spr;
			}
			break;			
			case 0x20:
			{
				opt.vel = vec3(spc, 0, 0) + curVec*emb.spr;
			}
			break;
			case 0x21:
			{
				opt.vel = vec3(0, spc, 0) + curVec*emb.spr;
			}
			break;
			case 0x22:
			{
				opt.vel = vec3(0, 0, spc) + curVec*emb.spr;
			}
			break;		
			case 0x15://rotate on time
			{
				float aa = 2 * PI / count, ab = g_fGlobalTime * emb.fv.x ;
				float a = aa*i+ab;// -PI + 2 * PI*(ik)+;
				opt.vel = (bool(gm & GMODE_RotOnY) ? (vec3(sin(a), 0, cos(a))* mRV) : vec3(sin(a), 0, cos(a))) * spc + curVec*emb.spr;
			}
			break;	
			case 0x16://rotate on x*(2PI) ,  x premultipied 2PI by CPU
			{
				float aa = 2 * PI / count, ab = (1-PTC.timer/PTC.life) * emb.fv.x ;
				float a = aa*i+ab+ emb.fv.y;// -PI + 2 * PI*(ik)+;
				opt.vel = (bool(gm & GMODE_RotOnY) ? (vec3(sin(a), 0, cos(a))* mRV) : vec3(sin(a), 0, cos(a))) * spc + curVec*emb.spr;
			}
			break;	

			case 0x8: //Fibonacci Sphere 
			{
				opt.vel = FibonacciSphere(count,i)* spc + curVec*emb.spr;
			}
			break;
 
			case 0x18:
			{

				opt.vel = SixWay[i%6].xyz*spc;

			}
			break;	
			case 0x12: //ring zy 1
			{
				float a = 2 * PI*(ik);
				opt.vel = vec3(0, sin(a), cos(a)) *(spc + length(curVec)*((gm&GMODE_WITH_SPR2)>>9)*emb.spr)+ ((gm&GMODE_WITH_SPR)>>8)*curVec*emb.spr;
			}
			break;		

#if 0
			case GMODE_TYPE_CLOUD: //pt cloud
			{

				uint idStart= emb.uiv.x;
					
				opt.vel = (curVec*emb.spr)+(PCPts[idStart+i].pos.xzy* mRV)*spc;
				opt.pos = curPos+(PCPts[idStart+i].pos.xzy* mRV)*emb.fv.x;
				opt.col.a = PCPts[idStart+i].alpha;
			}
			break;
#endif
			// case 0x1a: //ring reb
			// {
			// 	float a = atan(opt.vel.y, opt.vel.x) + 2 * PI*(ik); ;
			// 	opt.vel = vec3(cos(a), sin(a), 0) * (spc + length(curVec)*emb.spr)* pow(0.7, Emb[opt.eid].reb - opt.reb);
			// }
			// break;
			//case 0x1d:	opt.vel =  getVecRotY(curVec)*(spc + length(curVec)*emb.spr);			break;

			case 0x30:
			{
				float a = -PI + 2 * PI*(ik)+g_fGlobalTime;
				opt.vel = vec3(sin(a), 0, cos(a)) * spc;
			}
			break;			
				
			default:
			{
				opt.vel = (curVec*emb.spr + vRand*(spc + i*randFac));
			}
			break;
			}

		}
		uint uVelRtt=emb.gmode&GMODE_VelRotateMask;
		if (bool(uVelRtt))
		{			 
			//opt.vel = opt.vel *199;
			if (uVelRtt==0x01000000) opt.vel = mat3(mV) * opt.vel;  // rotation matrix's inverse = transpose
			else opt.vel *= mat3(mCustom) ;
		}

		float addLifeTime = (bool(emb.gmode&GMODE_LifeOnPv0)?opt.pv.x:  (bool(emb.gmode&GMODE_LifeOnPv1)?opt.pv.y:    0.0) );
		opt.timer = opt.life =  max(0.00001f,emb.life+ addLifeTime + rand(randFac+opt.ofs.x,uv)*emb.life1) ;		//life>0 	
		if (bool(emb.gmode & GMODE_VelOnEqvPeak)) 
		  opt.vel *= eqvAvgPeak;
		
		if (bool(emb.cflag & CFLAG_CycStartCvt))
			opt.timer=0.0;
		if (emb.sst.x>0.00001) {
		 	opt.stopTimer=emb.sst; 
			if (bool(emb.gmode&GMODE_LifeOnPv0)) opt.stopTimer.y+=opt.pv.x;
		}
		if (bool(emb.gmode & GMODE_GenTonPrT)) 
			opt.timer *= origRatio;  //life>0   max(0.00001f...), PTC is last value
		if (bool(emb.gmode & GMODE_Retarget)) 
			opt.tcl= opt.pos;
		if ( freeCount> CS_LOCAL_SIZE_X*16)
		GenParticle(opt);
	}

}




void main() 
{
	// Current SSBO index
	uint index = gl_GlobalInvocationID.x;
	if (index >= showCount) 
		return;	
	vec2 uv=vec2(g_fGlobalTimeFMod,randFac*17);

	uint pid= ids[index];
#if 0
	if (g_fElapsedTime==0.0f)
	{
		OutParticle(pid);
		return;
	}
#endif
	VSParticle ptc = PTC;
	float origRatio= ptc.timer / ptc.life;
	//if (index==0)		f0=ptc.timer;

	bool bNotLastConv=false;

	const uint id=ptc.eid;
    Ember emb=Emb[id];
	//if (id >= FW_MAX_TYPE_COUNT)	{FreeParticle(pid);		return;}
	uint mf=emb.mf; vec4 fv=emb.fv;
	bool needCvt = true;	
	bool stopped =false;//timer stop
	
	vec3 oldPos=ptc.pos;
	ptc.pos = ptc.pos+ ptc.vel*g_fElapsedTime;
	// (ptc.life<0.001?min(ptc.timer,g_fElapsedTime):g_fElapsedTime);  //to see if need

#if USE_MFLAG_LAND

#if  0  //front shiled 
	vec3 toEye=ptc.pos-eyePos;
	//if (dot(normalize(toEye),vec3(0,0,1))<0.23) 	{FreeParticle(pid);		return;}

	float toEyeDis=length(toEye);
	#define GLASSDIS 20
	if (toEyeDis<GLASSDIS)
	{ 
			//{FreeParticle(pid);		return;}
		vec3 dir=normalize(ptc.pos-eyePos);
		//if (bool(mf & MF_PtMovAt)) ptc.timer=0.0;
		mf=0;
		ptc.vel = //vec3(0,0,900) +		dir*1110;//
		reflect(vec3(0,0,-1),dir)*1090;
		//vec3(0);
		ptc.pos = eyePos+ dir*GLASSDIS;
 
		ptc.col*=0.98;
		//if (toEyeDis<20)		{FreeParticle(pid);		return;}
	}
#endif


 
	vec4 lv=emb.lv;
	//float radius = emb.r *	CalcRatio(origRatio, uint(emb.mpr)) *fireScale;

	//const float ldy=0.0f;	
	float ldy=landMin.y+lv.x;

	if ( ptc.mid==0 )
	{
		if (  ptc.pos.y <= ldy) 
		{			
			ptc.pos.y = ldy;// //ldy * 2 - ptc.pos.y;
			if (ptc.vel.y <0)
			ptc.vel.y = -ptc.vel.y* lv.y; //
			ptc.vel.xz*= lv.z;
			//ptc.vel+=   normalize(RandomDir( randFac + ptc.ofs.x,uv, 1.91)) *lv.zyz;
			ptc.timer*= lv.w;
			//ptc.timer=0;
			//	{FreeParticle(pid);		return;}
		}
	}
	else 
	{
		VtxMatStruct vd=vtxMats[ptc.mid];
#if FW_LIMIT_BOX
		const float lr=250.f;
		if (abs(ptc.pos.y) > lr	) {
				ptc.pos.y = (ptc.pos.y>0?2:-2)* lr- ptc.pos.y;
				ptc.vel.y = -ptc.vel.y; //
				//ptc.vel.xz*= -1;		
			}
		if (abs(ptc.pos.x) > lr	) {
				ptc.pos.x = (ptc.pos.x>0?2:-2)* lr- ptc.pos.x;
				ptc.vel.x = -ptc.vel.x; //
				//ptc.vel.yz*= -1;		
			}
		if (abs(ptc.pos.z) > lr	) {
				ptc.pos.z = (ptc.pos.z>0?2:-2)* lr- ptc.pos.z;
				ptc.vel.z = -ptc.vel.z; //
				//ptc.vel.xy*= -1;		
			}
#endif

		if (vd.cvt!=0) {	ptc.reb=0;	 ptc.timer=0;ptc.stopTimer.x=0.0001f; needCvt=true; 	}
		#if 0
			if (vd.landType==1) //ball
			{
				if (length(ptc.pos)>200)	{
						ptc.vel=normalize(-ptc.vel + RandomDir(ptc.vel.x+ randFac + ptc.ofs.x,uv, 1.17f)*300)*length(ptc.vel);  
				}
			}
		#endif
#if 0
 
#else
		if (vd.spec==1)
		{
		vec3 norm = mat3(vd.m) * vec3(0,0,-1);
		vec3 toEye=normalize(eyePos-ptc.pos);
		vec3 lightDir = normalize(lightPos-ptc.pos);
		//if (dot(norm,toEye)<0)			norm=-norm;
		//vec3 reflection = reflect( - lightDir,norm );//
		vec3 ha = normalize(( lightDir+toEye ));
		ptc.specAdd = pow(  abs(dot(ha, norm)), 8.f)  ;
		}
#endif
	}

#else
	if ( ptc.mid!=0 )
	{
		if (vtxMats[ptc.mid].cvt!=0) {	ptc.reb=0;	 ptc.timer=0;ptc.stopTimer.x=0.0001f; }

	}
#endif

	uint emb_cf=	emb.cflag;
	float velRatMul= bool(mf&MF_Dec_on_OrigRat)? origRatio*fv.w+1.f-fv.w   : 1.f - fFrameDecelerate * emb.decml;
    ptc.vel = ptc.vel * velRatMul + g_vFrameGravity * emb.m;

#if FW_HAS_STAGE
	bool isCycleEmb=bool(emb_cf & CFLAG_CycleCvt);
	if (isCycleEmb && (emb.stage < cycleStage || ptc.bid<expireBidMax))
	{
		ptc.reb=0;	ptc.timer=0;FreeParticle(pid);
	}
#endif



	if ( ptc.stopTimer.x<=0.0)
	{
		if (bool(emb_cf & CFLAG_UpdateStop)) //8000
		{
			if (ptc.stopTimer.y>0)
			{
				Pts[pid].stopTimer.y-=g_fElapsedTime;		
				OutParticle(pid);
				
				stopped = true;needCvt=false;
				//return;
			}
			else if (bool(emb_cf & CFLAG_UpdateDecA))  //C000
			{
				if (ptc.col.a>0.01f)
				{
					Pts[pid].col.a-=g_fElapsedTime;
					OutParticle(pid);
					stopped = true;needCvt=false;
					//return;
				
				}
				else {	ptc.reb=0;	ptc.timer=0;FreeParticle(pid); return;}
			}	
		
			if (stopped)
			{
				if (!bool(emb_cf&CFLAG_UpdateStopCV1))
					return;
				if (emb.tt1!=0)
				{
					if (emb.c1rat>0.1001 || rand01(randFac*7.89167+ptc.ofs.x+ptc.ofs.y*0.587,uv)>emb.c1rat  )
						return;
					uint id1=id + emb.tt1;
					if (bool(Emb[id1].cflag&CFLAG_UpdateStop) || Emb[id1].r<0.32  )				return;
				}
			}
		}		
	}
	else ptc.stopTimer.x-=g_fElapsedTime;

	if (!stopped)
    if ((mf & MF_MASK_Mov) == 0)
    {
        ptc.timer -= g_fElapsedTime;
        if (ptc.timer > 0.f)
        {
            needCvt = false;
			PTCBACK;
			//if ( int(ptc.bid)<maxBid)
            OutParticle(pid);

        } 
		else ptc.timer=0.0;
    }
    else
    {

        vec3  vt;
	
		if (bool(mf & MF_PtMovAt))
		{
			
			uint mtpid= (mf & MF_PtMovAtPIdMask)>>8;
			vec3 tgtPos=  mtpid!=0?  mtpPos[mtpid].xyz:
			ptc.tcl;
            vt = (tgtPos - ptc.pos);
			
            float vtLen = max(0.0001f, length(vt));

			if (vtLen < fv.y)
            {
				if ((mf & MF_PtMovInNoSpdChg)!=0){
				vec3 v=vt / pow(vtLen,fv.w);
                ptc.vel =(ptc.vel+  fv.x * v *g_fElapsedTime) ;
				}
				else if (dot(vt,ptc.vel)<0)
				{
					ptc.pos = tgtPos;
					ptc.vel = vec3(0.0);
				}
				else
				{
					if ((mf & MF_PtMovInTo)!=0)
					{
						if (length(ptc.vel)*g_fElapsedTime<fv.y)
						{
							ptc.pos = tgtPos;
							ptc.vel = vec3(0.0);
						}
						else
							ptc.vel*=pow(fv.z,10);
					}

				}
					if ((mf & MF_PtMovInCov)!=0)
						ptc.timer = 0.0;
            }
 
			else
            {
				vec3 v=vt / pow(vtLen,fv.w);
				// vec3 nv=normalize(v);
				// float lv=length(v);
				// v= min(10,lv)*nv;
                ptc.vel =(ptc.vel+  fv.x * v *g_fElapsedTime)*fv.z ;
            }

		}

		else if (bool(mf & MF_PtMovAtPath))
		{
			vec4 fv=emb.fv;
            vt =  vec3( cos((ptc.pos.z+g_fGlobalTime*fv.z)/fv.y ), 0,-cos((ptc.pos.x+g_fGlobalTime*fv.z )/fv.y)) ;
			//normalize(cross(ptc.pos,vec3(0,1,0)));
			ptc.vel +=   ((vt)*fv.x) ;

		}
		else if (bool(mf & MF_PtMovRand))
		{
			vec4 f=emb.fv;
            vt =  normalize(RandomDir(f.w + randFac + ptc.ofs.x,uv, 1.91f));
			//normalize(cross(ptc.pos,vec3(0,1,0)));
			ptc.vel +=    ((vt)*f.x) ;
		}

		//fv1 affected by pointer
		if (bool(mf & MF_PtMovAtG) )
		{
					vec4 fv1=emb.fv1;		
			for (int i=0;i<downPtrCount;i++)
			{

				vt = (pointerPos[i].xyz - ptc.pos);

				float vtLen = max(fv1.y, length(vt));

				ptc.vel +=   (vt*fv1.x + pointerVel[i].xyz*fv1.z	)/ pow(vtLen,fv1.w ) ;
				
			}
		}

		ptc.timer -= g_fElapsedTime;
		if (ptc.timer > 0.f)
		{
			needCvt = false;
			PTCBACK;
			//if ( int(ptc.bid)<maxBid)
			OutParticle(pid);
		} 
		else ptc.timer=0.0;

    }

	bool needFree=needCvt;
	if (needCvt)
	{

		if (ptc.reb>0)
		{
#if FW_HAS_STAGE
			if (!isCycleEmb)
#endif
			ptc.reb--;
		}
		bNotLastConv = (ptc.reb > 0);

		if (bNotLastConv)
		{
			if (rand01(randFac*7.89167+ptc.ofs.x+ptc.ofs.y*0.587,uv)<emb.c2skr-length(ptc.vel)*emb.c2spd  )
				needCvt=false;
			//vec3 vRand = normalize(RandomDir(randFac*1.89 + ptc.ofs.x*2.79,uv, 2.791f));

			ptc.timer = ptc.life = max(0.00001f, emb.life + (rand01(randFac*1.89 + ptc.ofs.x*2.79,uv))*emb.life1);
			PTCBACK;
			//if ( int(ptc.bid)<maxBid)
			OutParticle(pid);
			needFree=false;
		}
	}
	
	VSParticle opt = ptc;
	if (needCvt)
	{
		//opt = ptc;

		uint genEid = id + (bNotLastConv ? emb.tt2 : emb.tt);
		uint epf=Emb[genEid].pflag;
		if ( (epf & PFLAG_GenOfsMask)!=0 && (epf & ptc.flag)!=0   ){
				genEid += 1;
				//opt.flag = opt.flag & (~PFLAG_ConvOfs_MASK);
		}
		if (genEid != id)
		{
			if (Emb[genEid].gmode == GMODE_ExtDataCv)	//F0
			{
				if (Emb[genEid].tt!=0) generateEmber(uv,pid,genEid+Emb[genEid].tt,origRatio,ptc);
				if (Emb[genEid].tt1!=0) generateEmber(uv,pid,genEid+Emb[genEid].tt1,origRatio,ptc);	
				if (Emb[genEid].tt2!=0) generateEmber(uv,pid,genEid+Emb[genEid].tt2,origRatio,ptc);
			}
			else 
			{
				generateEmber(uv,pid,genEid,origRatio,ptc);
			}
		}
	}
	else if (canUpdateGen==1)
	{

		int cv1=emb.tt1 ;
		if (cv1 != 0 && (ptc.flag & PFLAG_FORBID_CV1)==0)
		{
			if (bool(emb_cf&CFLAG_UpdateStopCV1) && !stopped)	{ if (needFree) FreeParticle(pid);			return;}
			//if (rand01(randFac*7.89167+ptc.ofs.x+ptc.ofs.y*0.587,uv)>emb.c1rat  )				return;

			opt.eid = id + cv1;
#if 0
			if (Emb[opt.eid].gmode == GMODE_ExtDataCv)			
			{
				if (Emb[opt.eid].tt!=0) generateEmber2(pid,Emb[opt.eid].tt,ptc,opt);
				if (Emb[opt.eid].tt1!=0) generateEmber2(pid,Emb[opt.eid].tt1,ptc,opt);	
				if (Emb[opt.eid].tt2!=0) generateEmber2(pid,Emb[opt.eid].tt2,ptc,opt);				
			}
			else generateEmber2(pid,0,ptc,opt);
#else
	
			Ember emb = Emb[opt.eid];

			mat3 mRV;
			if ((emb.gmode & GMODE_RotOnY)!=0) mRV = GetRotYMatFromVec(PTC.vel);

			//opt.ofs = ptc.ofs;
            if (bool(emb.gmode & GMODE_GenZonPR))
				opt.ofs.z = CalcRatio(origRatio, Emb[ptc.eid].mpr);
			float timerRatio=1.0;
			if (bool(emb.mpr & 0xFF00))
				timerRatio=origRatio;//CalcRatio(origRatio, emb.mpr>>8);
			uint count = min(32, emb.emc);

			float spc = emb.spc * (bool(emb.cflag & CFLAG_VelonRatio) ? (origRatio): 1.f);

			opt.reb = emb.reb;

			//opt.col = vec4(DW2B_R(Emb[opt.eid].bcol), DW2B_G(Emb[opt.eid].bcol), DW2B_B(Emb[opt.eid].bcol), DW2B_A(Emb[opt.eid].bcol)) / 255.0f;

			vec3 baseVel = ptc.vel*emb.spr;

			uint gmt=emb.gmode & 0xFF;
            switch (gmt)
            {

                default:
				{

                    float epc = g_fElapsedTime / count;
                    for (uint i = 0; i < count; i++)
                    {
                        float ir = float(i) / float(count);
                        opt.pos =  ptc.pos - ir * ptc.vel * (1.0f/60); //

                        vec3 vRand = (RandomDir(ir*0.30867 + ir*randFac + ptc.ofs.x+ ptc.timer*0.859 + opt.pos.x, uv,117.31f));
                        opt.vel = (baseVel + normalize(vRand) * spc);
                        opt.life = max(0.00001f, emb.life + rand(randFac + ptc.ofs.x,uv) * emb.life1);
                        opt.timer = opt.life*timerRatio - ir/60.f;
                        opt.ofs.x += vRand.x; // fmod(opt.ofs.x + vRandom.x, 1.f);
								if ( freeCount> CS_LOCAL_SIZE_X*128 )		GenParticle(opt);

                    }
                }
                break;
				case 0x15: 
				{
				//opt.timer = emb.life;

                    float aa = 2 * PI / count, ab = g_fGlobalTime * emb.fv.x;
                    float vc = (bool( emb.cflag & CFLAG_SpcOnZ)?opt.ofs.z: 1.0);

                    float fRand = rand(randFac*1.89 + ptc.ofs.x*2.79,uv);
                    opt.life = max(0.00001f, emb.life + fRand * emb.life1);
                    opt.timer = opt.life*timerRatio;
                    for (uint i = 0; i < count; i++)
                    {
                        float a = aa * i + ab;
						vec3 dir= (bool(emb.gmode & GMODE_RotOnY) ? (vec3(sin(a), 0, cos(a))* mRV) : vec3(sin(a), 0, cos(a))) ;
                        opt.vel = dir* spc + baseVel;
                        opt.pos = ptc.pos + dir*vc*emb.fv.z;
						if ( freeCount> CS_LOCAL_SIZE_X*128 )		GenParticle(opt);
                    }
                }
                break;
			case 0x16://rotate on x*(2PI) ,  x premultipied 2PI by CPU
			{
				float aa = 2 * PI / count, ab = (1-PTC.timer/PTC.life) * emb.fv.x ;
                    float vc = (bool( emb.cflag & CFLAG_SpcOnZ)?opt.ofs.z: 1.0);

                    float fRand = rand(randFac*1.89 + ptc.ofs.x*2.79,uv);
                    opt.life = max(0.00001f, emb.life + fRand * emb.life1);
                    opt.timer = opt.life*timerRatio;
                    for (uint i = 0; i < count; i++)
                    {
                        float a = aa * i + ab+ emb.fv.y;
 						vec3 dir= (bool(emb.gmode & GMODE_RotOnY) ? (vec3(sin(a), 0, cos(a))* mRV) : vec3(sin(a), 0, cos(a))) ;
                        opt.vel = dir* spc + baseVel;
                        opt.pos = ptc.pos + dir*vc*emb.fv.z;
						if ( freeCount> CS_LOCAL_SIZE_X*128 )		GenParticle(opt);
                    }
			}
			break;
            }

#endif
		}

	}

	//ptc.timer= ptc.timer- g_fElapsedTime;
	//ptc.pos += ptc.vel * g_fElapsedTime;
	//if (index==0)		{f1=ptc.timer;f2=g_fElapsedTime;}

	if (needFree) FreeParticle(pid);
}
