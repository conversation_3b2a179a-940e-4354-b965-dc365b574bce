#version 460
precision highp float;
precision highp int;
#extension GL_GOOGLE_include_directive : enable

#define HAS_TEXTURE 0
#define USE_DEPTH_BLUR 0
#include "GsParticleShared.h"

// layout(push_constant) uniform PushConsts {
// 	uint kld;
// } PCs;

layout (local_size_x = CS_LOCAL_SIZE_X) in;
#include "colorutil.glsl"

#define IS_CS 
#include "FwParticleShaderShared.glsl"

#if FW_HAS_IMG_COLOR
layout (binding = 4) uniform sampler2D samplerTex1;
#endif

const vec4 hsls[2]={vec4(0,1,0,1),vec4(1,0,0,1)};
const vec3 rainbowRGB[8]={ vec3(1,0,0),vec3(1,0.5,0),vec3(1,1,0),vec3(0,0.5,0),vec3(0,0,1),vec3(75.0/255.0,0,130.0/255.0),vec3(147.8/255.0,0,211.0/255), vec3(1,0,0) };

float rand01(float seed, in vec2 uv)//-1 ~ 1
{
	float result = fract(sin(seed * dot(uv, vec2(12.9797f, 78.733f))) * 39758.5253f);
	return result;
}

float rand(float seed, in vec2 uv)//-1 ~ 1
{
	float result = fract(sin(seed * dot(uv, vec2(12.9898f, 78.733f))) * 39758.5253f);
	//seed += 1.0f;
	return result*2-1;
}
vec3 RandomDir(float fOffset, vec2 uv,float fDiv)
{
	//float tCoord = fOffset;//(g_fGlobalTime + fOffset) / fDiv + randFac;
	return vec3(rand(fOffset,uv*0.198),rand(fOffset+17.251,uv*0.231),rand(fOffset+231.27912,uv*0.137));
}

vec3 rainbow(float x)
{
	float x7=fract(x)*7.0;
	int c1=int(x7);
	float r=fract(x7);
	return mix(rainbowRGB[c1],rainbowRGB[c1+1],r);
}

#define PTC Pts[pid]	//VSParticle ptc= Pts[pid];

void main() 
{
	// Current SSBO index
	uint index = gl_GlobalInvocationID.x;
	//if (index >= showCount) 		return;	

	//if (index==0)	{	}

	uint pid= idsOut[index];

	VSParticle ptc = PTC;
	Ember emb = Emb[ptc.eid];
	uint cf = emb.cflag;
	//if (emb.r==0.0)	return;// error: vtx do not know skip
#if USE_DEPTH_BLUR
	float dpv=length(eyePos-ptc.pos)-stdDistance;
	dpv=1+abs(dpv*3/stdDistance);
#endif
	vec2 uv=vec2(g_fGlobalTime,randFac*17);
	mat3 mRV= mat3(1);
	float ratioO = ptc.timer / ptc.life ;
	//ratioO=clamp(ratioO,0,1);
	float radius = //(( cf & CFLAG_SpcOnZ)!=0u ? ptc.ofs.z : 1.f)*
	emb.r *	CalcRatio(ratioO, uint(emb.mpr)) *fireScale * (bool(cf & CFLAG_RMul)?ptc.ofs.z:1.f)
#if USE_DEPTH_BLUR
	* pow(dpv,2)
#endif
	;
#if 1
	vec4 c ;
	uint tid = (emb.tflag & TFLAG_ColMaskIds);
	if (bool(tid))
	{
		float ratio = min(ratioO,0.999999);
		if (bool(emb.tflag & TFLAG_ColMaskInv))
		{
			ratio =1.f-ratio;	 	
		}
		float ratioInv = 1 - ratio;
		float rndOfx = (bool(emb.tflag&TFLAG_ColMaskRandom) ? ptc.ofs.x : 0.f);
        float ratioT = ratioInv * emb.fCol2.b;  //fCol2 order : WXYZ   ARGB 
		if (bool(emb.tflag & TFLAG_ColHueMask))
		{
#if FW_HAS_IMG_COLOR	
			c= texture(samplerTex1, vec2(ptc.pos.x/(5*768.0)+0.5,0.5-ptc.pos.y/(5*433.0)));//  
			vec3 hsl;
			if ((cf & 0xF00)==0x400) hsl=(RGBtoHSL(c.rgb)); 
			else hsl= (RGBtoHSL(ptc.col.rgb)); 
#else
			vec3 hsl = (RGBtoHSL(ptc.col.rgb)); 
#endif					
			switch (tid)
			{
				case TFLAG_ColHSL://7  W X Y : Hm Sm Lm 
				{
					vec3 amm= mix( vec3(0,1,1),emb.fCol2.wxy,fract(ratioT));
                    hsl = clamp(vec3(fract(hsl.r+amm.x),hsl.g*amm.y,hsl.b*amm.z),0.0,1.0);
					break;
				}
				case TFLAG_ColHSLSin://8
				{
					vec3 amm= mix( vec3(0,1,1),emb.fCol2.wxy, (1.f - cos(2.f * PI * (ratioT + rndOfx))) / 2);
                    hsl = clamp(vec3(fract(hsl.r+amm.x),hsl.g*amm.y,hsl.b*amm.z),0.0,1.0);
					break;
				}	
				case TFLAG_ColHue://1    W X Y : Hm Ha S 
				{
                    hsl = vec3(fract(hsl.r + emb.fCol2.r + fract(ratioT) * emb.fCol2.a + rndOfx), hsl.g * emb.fCol2.g, hsl.b);
					break;
				}
				case TFLAG_ColHueSin://2
				{
                    hsl = vec3(fract(hsl.r + sin(2 * PI * (emb.fCol2.r + ratioT + rndOfx)) * emb.fCol2.a), hsl.g * emb.fCol2.g, hsl.b);
					break;
				}
				case TFLAG_ColLight://5
				{
                    hsl = vec3(hsl.r, hsl.g, 
					clamp(hsl.b * 	emb.fCol2.a 									// L
					+ hsl.b * 		emb.fCol2.r * fract(ratioT + rndOfx) 			// 0~L
					+ (1 - hsl.b) * emb.fCol2.g * fract(ratioT + rndOfx),0.f,1.f));	// L~1
					break;
				}
				case TFLAG_ColLightSin://6
				{
                    float sn = (1.f - cos(2.f * PI * (ratioT + rndOfx))) / 2;
					hsl = vec3(hsl.r, hsl.g, clamp(hsl.b*emb.fCol2.a+(hsl.b*emb.fCol2.r + (1 - hsl.b)*emb.fCol2.g)*sn ,0.f,1.f));

					break;
				}
				
			}
			c = vec4(HSLtoRGB(hsl), ptc.col.a*emb.fCol.a);
		}
		else 
		{
			switch (tid)
			{
			default:
			case 0:
			{
				c = ptc.col;
				break;
			}
			// case 0x30: 
			// {
			// 	c=mix(hsls[0],hsls[1],1-ratioO);//
			// }
			// break;
			case TFLAG_ColToN:
			{
				c = ptc.col *ratio + emb.fCol2 * ratioInv;
				break;
			}
			case TFLAG_AlphaOnSpd:
			{
				
				float vl=length(ptc.vel);
				c= ptc.col*min( 0.618 + vl/5000 ,1.0);
				radius = radius*min(1+vl/1000,2);
				break;
			}

			}
		}
	}
	else
	{
		
#if 0
		c = ptc.col;
#else
		vec4 efCol = emb.fCol;


		switch ((cf & 0xF00)>>8)
		{

		// case 0x10: {
		// 	opt.col.rgb = ptc.tcl;
		// }break;
		case 1: { //rgba  	0x100
			c = vec4(ptc.col.rgb, ptc.col.a*efCol.a);
		}break;
		case 2: { //rgb		0x200
			c = vec4(ptc.col.rgb, efCol.a);
		}break;
		case 3: { //rgb		0x300
			c = ptc.col*efCol;
		}break;
#if FW_HAS_IMG_COLOR
		case 4: { 		
			//c= texture(samplerTex1, vec2(ptc.tcl.x/1920.0+0.5,0.5-ptc.tcl.y/1080.0));//  
			c= texture(samplerTex1, vec2(ptc.pos.x/1920.0+0.5,0.5-ptc.pos.y/1080.0));//  

		}break;			
#endif		
		case 7: { //rgb		0x700
			c=vec4(rainbow(1.0-ratioO), ptc.col.a*efCol.a);
			
		}break;		
		case 8: { //rgb		0x800 Depth
			float dis=length(ptc.pos-eyePos);
			c.rgb= vec3( (1.0-(dis-10.0)/1000.0) );
			c.a=1;
		}break;		

		case 9: { //rgb		0x900
			float ccc=ratioO*ratioO*ratioO;
			c = vec4(ratioO,ccc,ccc*ccc, efCol.a);
		}break;
		
		// case 8:{
		// 								c=vec4(HSLtoRGB(vec3( fract(1.f + ptc.ofs.y - vRand.z/12 ), 1.0, 0.5)),efCol.a);
		// }break;
			//case 0x20000: { //CFLAG_ImgTex,, need switch 0x2FFFE!!!
			//	opt.col.ra = float2(ptc.col.r*emb.r, efCol.a);
			//}break;

		default:
			c = efCol;
			break;
		}
#endif
				
	}

#else

	vec4 c = ptc.col;
#endif



	//if (ptc.timer <= 0.f) ptc.col = 0;  //first emb timer=0 use 0 col, then do not need this, and this will discard last 
#if HAS_BAND_V
	if ((emb.tflag & TFLAG_OnBandV_Mask)!=0u && ptc.bid < PT_BANDS ) {
		vec4 bv = bandv[ptc.bid];

		vec4 bdRate = vec4(
			float((emb.tflag & TFLAG_OnBandV_H) >> 24),
			float((emb.tflag & TFLAG_OnBandV_S) >> 20),
			float((emb.tflag & TFLAG_OnBandV_L) >> 16),
			float((emb.tflag & TFLAG_OnBandV_A) >> 28)
			)/15.f;

		vec3 hsl = (RGBtoHSL(c.rgb));
		hsl *= (1.f - bdRate.xyz + bdRate.xyz * bv.x);  
		c.rgb = HSLtoRGB(hsl);
		c.a *= (1.f - bdRate.a + bdRate.a * bv.x);
		//output.color *= (1.f - bdRate + bdRate *bv.x);

	}
#else
	//if ((emb.tflag & TFLAG_OnBandV_Mask)!=0u)	c.a *=(eqvAvgPeak);
#endif

	float r = CalcRatio(ratioO, uint(emb.mpc));
	c.a *= r*c.a;
#if USE_DEPTH_BLUR
	c.a=c.a/pow(dpv,2);
	c=c/dpv;
#endif

#if 0
	vec3 lightVector1 =vec3(0,0,1);
	vec3 bumpNormal=normalize(ptc.vel);
	//float lightIntensity = max(0.f,dot(bumpNormal, -lightVector1));
	vec3	reflection = normalize(reflect(ptc.pos-eyePos,  bumpNormal));   	// + 2.f * bumpNormal * bumpNormal.z;
	float specular = pow(max(0.f,abs(dot(reflection, lightVector1))),8); 
	c.rgb=c.rgb+vec3(specular)*.5;
#elif 0 
	c.rgb+=vec3(sin(length(eyePos-ptc.pos)))*0.66;
#endif
	
#if 1
		if (bool(cf & CFLAG_PtrLight) )
		{				
		//	float vtLen2 = max(1.f, distance( pointerLightPos.xyz,ptc.pos));
		//		c.rgb *=   emb.cv.x + emb.cv.y/ (vtLen2*vtLen2) ;		
			vec4 cv=emb.cv;
			float addv = cv.x;
			for (int i=0;i<ptrLightCount;i++)
			{

				float vtLen = max(1.f, length(pointerLight[i].xyz - ptc.pos));
				//if (vtLen<cv.z)
				addv +=   pointerLight[i].w*cv.y/pow(vtLen,cv.w ) ;
				
			}		
			c.rgb*=addv;
		}
		
#endif
	c.rgb*=emb.ocm;
	c.rgb+=ptc.specAdd;
	
	//radius = length(vec4(radius, 0, 0, 0)*mW );

	//GS===========
#define KLD (HAS_KALEIDO && !OUTPTLIST)

#if KLD
	vec2 p=ptc.pos.xy;
	//vec3 hsl = RGBtoHSL(c.rgb);
	

	for (int j = 0; j < kldCount; j++)
	{

#if KLD_MIRROR
		float radiusi=radius;
		// if (j>0 && bool(ptc.flag & PFLAG_NO_KLD))
		// {
		// 		radius=0;
		// }
		// else
		{
			float a = (j/2) * PI *4 /kldCount;


			float ca = cos(a), sa = sin(a);
			float b = bool(j % 2==0)? 1.0:-1.0;// +sin(g_fGlobalTime / 2)*PI / 12;

			//ptc.pos.xy = vec2(p.x*ca - p.y*sa, (p.y*ca + p.x*sa)*b);
			
			ptc.pos.xy = vec2((p.x - 0)*ca - (p.y - 1000)*sa + 0 ,((p.x - 0)*sa + (p.y - 1000)*cos(a))*b + 1000  );
			if (ptc.pos.y<0) radiusi=0;

// #if FW_WATER_MIRROR
// 		c *=(j>0?0.8:1);  // mirror darker
// #endif
		}
#else
		float a = j * PI *2 /kldCount;


		float ca = cos(a), sa = sin(a);
		ptc.pos.xy = vec2(p.x*ca - p.y*sa, (p.y*ca + p.x*sa));
					////	c.rgb = HSLtoRGB(vec3(fract(hsl.x+ 0.043 *abs(sin(PI*1)),hsl.yz));
		//c.rgb=rainbow(float(j)/kldCount); //RAINBOW	
#endif //KLD_MIRROR


#endif



		vec4 center =  vec4(ptc.pos, 1.0) ;  //mW
		if (ptc.mid>0) center*=vtxMats[ptc.mid].m;

#if FW_USE_TEXARRAY
		vec2 fa = vec2(	emb.texId, 0//	emb.ocm 	//float(cf & 1U)
		);
#endif

		vec4 pfv=vec4(1-ratioO,ratioO,ptc.life,ptc.ofs.y);

#ifdef OUTPTLIST 
		vtxs[index].color=c;
		vtxs[index].outpos = (center)*mP;
		vtxs[index].fv = pfv;	
#else


		int ptcmid=0;


#if 1 // cache ref ,optimize for performance 

#if KLD
		uint vi= index*kldCount+j;
		vec4 vtxoutpos = vec4(center.xyz,radiusi
		#if FW_USE_SCALESW
		*scalesW[ptcmid].x
		#endif
		);
#else
		uint vi= index;
		vec4 vtxoutpos = vec4(center.xyz,radius
		#if FW_USE_SCALESW
		*scalesW[ptcmid].x
		#endif		
		);
#endif		
    // Use structured writes
    vtxs[vi] = VtxOut(
       vtxoutpos,
        c,
		#if FW_USE_TEXARRAY
		vtxs[vi].text0 = vec4(ptc.mid!=0?1.0:0.0,0, fa.xy),
		#else
		vec4(0),
		#endif
        pfv
    );

#else

#if KLD
		uint vi= index*kldCount+j;
		vtxs[vi].outpos = vec4(center.xyz,radiusi
		#if FW_USE_SCALESW
		*scalesW[ptcmid].x
		#endif
		);
#else
		uint vi= index;
		vtxs[vi].outpos = vec4(center.xyz,radius
		#if FW_USE_SCALESW
		*scalesW[ptcmid].x
		#endif		
		);
#endif	
		vtxs[vi].color=c;
		#if FW_USE_TEXARRAY
		vtxs[vi].text0 = vec4(ptc.mid!=0?1.0:0.0,0, fa.xy);
		#endif
		vtxs[vi].fv = pfv;	

#endif  // cache ref ,optimize for performance 


	// //vec2 va=0.1 * vec2(rand(ptc.ofs.x,vec2(0.55,.324)),rand(ptc.ofs.x,vec2(0.77,.227)) );
	// 	for (uint i=0;i<4;i++)
	// 	{
	// 	vec4 outpos;
	// 	#if KLD
	// 	uint vi= index*kldCount*4+j*4+i;
	// 	#else
	// 	uint vi= index*4+i;
	// 	#endif

	// 	vec2 vtxpos=vec2((i & 2) >> 1,((i + 3) & 2) >> 1);//+va;//0,1  0,0  1,0  1,1

	// 	vtxs[vi].color=c;
	// 	vtxs[vi].outpos = (vec4(vec3( vec2(2.f,-2.f)*vtxpos.xy+vec2(-1.f,1.f), 0) * radius, 0.0) + center)*mP;
	// 	#if FW_USE_TEXARRAY
	// 	vtxs[vi].tex = vec4(vtxpos, fa.xy);
	// 	#else
	// 	//vtxs[vi].tex=vec4(vtxpos,0.0f,0.0f); 
	// 	#endif
	// 	vtxs[vi].fv = pfv;	
	// 	}


#endif

#if KLD
	}
#endif

}