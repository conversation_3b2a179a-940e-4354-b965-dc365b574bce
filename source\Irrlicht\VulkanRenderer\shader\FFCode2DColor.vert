#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"



	layout(location = 0) in vec3 i_pos;
	layout(location = 1) in vec3 i_norm;
	layout(location = 2) in vec4 i_color;
	layout(location = 3) in vec2 i_tex0;




	layout(location = 0) out vec4 o_colorD;
	layout(location = 1) out vec2 o_tex0;



// adding vertex shader code
void main()
{
	o_colorD =   i_color;
	//o.color.a= (input.norm.z/360.f);
	o_tex0=i_tex0;
    gl_Position = vec4( vec2(-1.f,1.f) + vec2(2.f,-2.f)*i_pos.xy/i_norm.xy, 0.0f, 1.0f);
}

