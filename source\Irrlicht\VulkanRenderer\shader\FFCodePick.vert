#version 460
#extension GL_GOOGLE_include_directive : enable


#include "FFCodeHeader.glsl"

	layout(location = 0) in vec3 i_pos;
	layout(location = 1) in vec3 i_norm;
	layout(location = 2) in vec4 i_color;
	layout(location = 3) in vec2 i_tex0;





	layout(location = 0) out vec2 o_tex0;
	layout(location = 1) out vec3 o_wNorm;       //world space normal
layout (location = 2) out flat int outiv;
void main( )   
{
	
	vec4 worldPos = vec4(i_pos,1) * g_mWorld ;
	vec4 cameraPos = worldPos * g_mView ; //Save cameraPos for fog calculations
	gl_Position =  cameraPos * g_mProj;

    outiv = gl_VertexIndex+1;
    	gl_PointSize = 16;
}


