#version 460

#extension GL_ARB_separate_shader_objects : enable
#extension GL_ARB_shading_language_420pack : enable
#define USE_LIGHT_TEX 0

layout (location = 0) in vec3 inPos;
layout (location = 1) in vec3 inNor;
layout (location = 2) in vec3 inEyePos;
layout (location = 3) in vec2 inUV;
layout (location = 4) in vec3 inNorV;
layout (location = 5) in flat int outiv;
layout (binding = 1) uniform UBO 
{
	mat4 	g_mWorld;
	mat4    g_mLightVP;
	vec4	Diffuse;
	vec4	Ambient;
	vec4	Specular;	
	vec4	lnv;
	ivec4	TextureModes;
	float   clipY, padf1, padf2, padf3;
	
	vec3	LightColor;
	int   	outNormalMap;

	vec3	LightDir;
	uint   pickId;

	
	vec4	TexMulFactor;
	vec4	TexAddFactor;

	vec4	ToonTexMulFactor;
	vec4	ToonTexAddFactor;

	vec4	SphereTexMulFactor;
	vec4	SphereTexAddFactor;

 

} ubo;

layout (binding = 2) uniform sampler2D Tex;
layout (binding = 3) uniform sampler2D ToonTex;
layout (binding = 4) uniform sampler2D SphereTex;
#if USE_LIGHT_TEX
layout (binding = 5) uniform sampler2D LightTex;
#endif
layout (location = 0) out uvec4 outFragColor;


//SHADOW ++++++++++++++++++++++++++++++++++++++++++++++++++++++++
#define ambient 0.1

float textureProj(vec4 shadowCoord, vec2 off)
{
	float shadow = 1.0;
	if ( shadowCoord.z > -1.0 && shadowCoord.z < 1.0 ) 
	{
		float dist = texture( SphereTex, (vec2(1,-1)*shadowCoord.st+1)/2 + off ).r;
		if ( shadowCoord.w > 0.0 && dist < shadowCoord.z*0.999999 )
		{
			shadow = ambient;
		}
	}
	return shadow;
}

void main() 
{

	outFragColor.w = (outiv<<8)|(ubo.pickId&0xFF);
	outFragColor.xyz = ivec3(inPos.x*1000,inPos.y*1000,inPos.z*1000);
 
	
}
