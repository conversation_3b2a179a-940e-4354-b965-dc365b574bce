#version 460
#extension GL_GOOGLE_include_directive : enable
#include "../DefineShareWithShader.h"
precision highp float;


layout (binding = 2)  uniform sampler2D g_tex0_sampler;
layout (binding = 3)  uniform sampler2D g_tex1_sampler;

layout(location = 0) in vec4 i_colorD;
layout(location = 1) in vec2 i_tex0;

//========================================= PS ========================================


//SamplerState g_tex1_sampler : register(s1);

layout (location = 0) out vec4 outFragColor;

// adding pixel shader
void main()
{

	float Y=texture(g_tex0_sampler,i_tex0).r;
	float u=texture(g_tex1_sampler,vec2(i_tex0.x, i_tex0.y/2 ) ).r;
	float v=texture(g_tex1_sampler,vec2(i_tex0.x, i_tex0.y/2 +0.5 ) ).r;
	vec4 YUVA=vec4(Y,
	u- 0.5		,
	v- 0.5		,
	1.0  );

#if RGBA_ORDER
	vec4 color	 = vec4(
				YUVA.x+		YUVA.y* 1.765,
		YUVA.x+		YUVA.y* -0.343	+	YUVA.z * -0.711,
		YUVA.x+                       YUVA.z*1.4,
		1.0)  ; 
#else
	vec4 color	 = vec4(
		YUVA.x+                       YUVA.z*1.4,
		YUVA.x+		YUVA.y* -0.343	+	YUVA.z * -0.711,
		YUVA.x+		YUVA.y* 1.765						,
		1.0)  ; 
#endif
		//color.a=(color.r+color.g+color.b)/2;
outFragColor = color;

}

