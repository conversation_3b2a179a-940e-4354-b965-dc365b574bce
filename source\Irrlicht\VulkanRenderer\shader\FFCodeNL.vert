#version 460
#extension GL_GOOGLE_include_directive : enable
#define USE_SPECULAR 0


#include "FFCodeHeader.glsl"

	layout(location = 0) in vec3 i_pos;
	layout(location = 1) in vec3 i_norm;
	layout(location = 2) in vec4 i_color;
	layout(location = 3) in vec2 i_tex0;





	layout(location = 0) out vec2 o_tex0;
	layout(location = 1) out vec3 o_wNorm;       //world space normal
	layout(location = 2) out vec4 o_colorD;
#if USE_SPECULAR 
	layout(location = 3) out vec4 o_colorS;
#endif
	layout(location = 4) out vec4 o_wPos;       //world space 
void main( )   
{
	
	o_wPos = vec4(i_pos,1) * g_mWorld ;
	vec4 cameraPos = o_wPos * g_mView ; //Save cameraPos for fog calculations
	gl_Position =  cameraPos * g_mProj;
	o_colorD = i_color.bgra *g_material.Diffuse;
		if (passEnum==0x10)
	o_tex0 = vec2(1-i_tex0.x,i_tex0.y) ;
	else
	o_tex0 = i_tex0 ;

gl_Position.z = min(gl_Position.z, gl_Position.w); //clamp z to 0-1, eg  SKY box

}


